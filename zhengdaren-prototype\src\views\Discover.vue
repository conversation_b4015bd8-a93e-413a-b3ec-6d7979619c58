<template>
  <div class="container">
    <div class="page-content">
      <!-- 搜索栏 -->
      <div class="search-header">
        <div class="search-input-wrapper">
          <i class="fas fa-search"></i>
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索政策、关键词"
            @keyup.enter="handleSearch"
            class="search-input"
          >
          <i v-if="searchQuery" class="fas fa-times clear-btn" @click="clearSearch"></i>
        </div>
        <button class="search-btn" @click="handleSearch">搜索</button>
      </div>

      <!-- 分类筛选 -->
      <div class="category-filter">
        <div class="filter-tabs">
          <div 
            class="filter-tab" 
            :class="{ active: activeCategory === category.value }"
            v-for="category in categories" 
            :key="category.value"
            @click="selectCategory(category.value)"
          >
            {{ category.name }}
          </div>
        </div>
      </div>

      <!-- 排序和筛选 -->
      <div class="sort-filter">
        <div class="sort-options">
          <span 
            class="sort-option" 
            :class="{ active: sortBy === option.value }"
            v-for="option in sortOptions" 
            :key="option.value"
            @click="changeSortBy(option.value)"
          >
            {{ option.name }}
          </span>
        </div>
        <div class="filter-btn" @click="showFilterModal = true">
          <i class="fas fa-filter"></i>
          筛选
        </div>
      </div>

      <!-- 政策列表 -->
      <div class="policy-results">
        <div class="result-info">
          <span>共找到 {{ filteredPolicies.length }} 条政策</span>
        </div>
        
        <div class="policy-list">
          <div 
            class="policy-card" 
            v-for="policy in paginatedPolicies" 
            :key="policy.id"
            @click="goToPolicyDetail(policy)"
          >
            <div class="policy-header">
              <h3 class="policy-title">{{ policy.title }}</h3>
              <div class="policy-status" :class="policy.status">
                {{ getStatusText(policy.status) }}
              </div>
            </div>
            
            <p class="policy-summary">{{ policy.summary }}</p>
            
            <div class="policy-info">
              <div class="policy-tags">
                <span 
                  class="tag" 
                  :class="getTagClass(tag)" 
                  v-for="tag in policy.tags.slice(0, 3)" 
                  :key="tag"
                >
                  {{ tag }}
                </span>
              </div>
              
              <div class="policy-meta">
                <span class="meta-item">
                  <i class="fas fa-calendar"></i>
                  {{ policy.publishDate }}
                </span>
                <span class="meta-item">
                  <i class="fas fa-eye"></i>
                  {{ policy.views }}
                </span>
              </div>
            </div>
            
            <div class="policy-actions">
              <button class="action-btn" @click.stop="toggleFavorite(policy)">
                <i :class="policy.isFavorite ? 'fas fa-heart' : 'far fa-heart'"></i>
                {{ policy.isFavorite ? '已收藏' : '收藏' }}
              </button>
              <button class="action-btn" @click.stop="sharePolicy(policy)">
                <i class="fas fa-share"></i>
                分享
              </button>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div class="load-more" v-if="hasMore">
          <button class="load-more-btn" @click="loadMore">
            加载更多
          </button>
        </div>
      </div>
    </div>
    
    <!-- 筛选弹窗 -->
    <div class="filter-modal" v-if="showFilterModal" @click="showFilterModal = false">
      <div class="filter-content" @click.stop>
        <div class="filter-header">
          <h3>筛选条件</h3>
          <i class="fas fa-times" @click="showFilterModal = false"></i>
        </div>
        
        <div class="filter-section">
          <h4>发布时间</h4>
          <div class="filter-options">
            <label v-for="option in timeOptions" :key="option.value">
              <input type="radio" v-model="filters.timeRange" :value="option.value">
              {{ option.name }}
            </label>
          </div>
        </div>
        
        <div class="filter-section">
          <h4>适用对象</h4>
          <div class="filter-options">
            <label v-for="option in targetOptions" :key="option.value">
              <input type="checkbox" v-model="filters.targets" :value="option.value">
              {{ option.name }}
            </label>
          </div>
        </div>
        
        <div class="filter-actions">
          <button class="btn btn-secondary" @click="resetFilters">重置</button>
          <button class="btn btn-primary" @click="applyFilters">确定</button>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'Discover',
  components: {
    BottomNavigation
  },
  data() {
    return {
      searchQuery: '',
      activeCategory: 'all',
      sortBy: 'latest',
      currentPage: 1,
      pageSize: 10,
      showFilterModal: false,
      filters: {
        timeRange: 'all',
        targets: []
      },
      categories: [
        { name: '全部', value: 'all' },
        { name: '企业政策', value: 'enterprise' },
        { name: '个人补贴', value: 'personal' },
        { name: '税收优惠', value: 'tax' },
        { name: '创业扶持', value: 'startup' },
        { name: '就业培训', value: 'employment' }
      ],
      sortOptions: [
        { name: '最新发布', value: 'latest' },
        { name: '最多浏览', value: 'popular' },
        { name: '相关度', value: 'relevance' }
      ],
      timeOptions: [
        { name: '全部时间', value: 'all' },
        { name: '最近一周', value: 'week' },
        { name: '最近一月', value: 'month' },
        { name: '最近三月', value: 'quarter' }
      ],
      targetOptions: [
        { name: '个人', value: 'individual' },
        { name: '企业', value: 'enterprise' },
        { name: '机构', value: 'organization' }
      ],
      policies: [
        {
          id: 1,
          title: '关于支持中小企业数字化转型的若干措施',
          summary: '为加快推进中小企业数字化转型，提升企业竞争力，制定本措施。支持企业开展数字化改造，给予资金补贴和技术支持。',
          publishDate: '2024-01-15',
          category: 'enterprise',
          tags: ['企业扶持', '数字化', '中小企业', '资金补贴'],
          views: 1250,
          status: 'active',
          isFavorite: false,
          targets: ['enterprise']
        },
        {
          id: 2,
          title: '个人所得税专项附加扣除暂行办法',
          summary: '为贯彻落实修改后的个人所得税法，现就个人所得税专项附加扣除有关事项制定本办法。包括子女教育、继续教育等扣除项目。',
          publishDate: '2024-01-10',
          category: 'personal',
          tags: ['个人税收', '专项扣除', '减税', '教育'],
          views: 2100,
          status: 'active',
          isFavorite: true,
          targets: ['individual']
        },
        {
          id: 3,
          title: '2024年创业担保贷款政策',
          summary: '为进一步做好创业担保贷款工作，支持创业带动就业，现就有关事项通知如下。提高贷款额度，降低申请门槛。',
          publishDate: '2024-01-08',
          category: 'startup',
          tags: ['创业扶持', '担保贷款', '就业', '金融支持'],
          views: 1800,
          status: 'active',
          isFavorite: false,
          targets: ['individual', 'enterprise']
        },
        {
          id: 4,
          title: '高新技术企业认定管理办法',
          summary: '为加强高新技术企业认定管理工作，规范认定行为，制定本办法。明确认定条件、程序和管理要求。',
          publishDate: '2024-01-05',
          category: 'enterprise',
          tags: ['高新技术', '企业认定', '税收优惠', '科技创新'],
          views: 1600,
          status: 'active',
          isFavorite: false,
          targets: ['enterprise']
        },
        {
          id: 5,
          title: '职业技能提升行动方案',
          summary: '为全面提升劳动者职业技能水平和就业创业能力，制定本行动方案。开展大规模职业技能培训，提升就业质量。',
          publishDate: '2024-01-03',
          category: 'employment',
          tags: ['技能培训', '就业', '职业教育', '补贴'],
          views: 1400,
          status: 'active',
          isFavorite: false,
          targets: ['individual']
        }
      ]
    }
  },
  computed: {
    filteredPolicies() {
      let result = this.policies

      // 分类筛选
      if (this.activeCategory !== 'all') {
        result = result.filter(policy => policy.category === this.activeCategory)
      }

      // 搜索筛选
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        result = result.filter(policy => 
          policy.title.toLowerCase().includes(query) ||
          policy.summary.toLowerCase().includes(query) ||
          policy.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }

      // 排序
      if (this.sortBy === 'latest') {
        result.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate))
      } else if (this.sortBy === 'popular') {
        result.sort((a, b) => b.views - a.views)
      }

      return result
    },
    paginatedPolicies() {
      const start = 0
      const end = this.currentPage * this.pageSize
      return this.filteredPolicies.slice(start, end)
    },
    hasMore() {
      return this.paginatedPolicies.length < this.filteredPolicies.length
    }
  },
  methods: {
    handleSearch() {
      this.currentPage = 1
      // 实际项目中这里会调用API
    },
    clearSearch() {
      this.searchQuery = ''
      this.handleSearch()
    },
    selectCategory(category) {
      this.activeCategory = category
      this.currentPage = 1
    },
    changeSortBy(sortBy) {
      this.sortBy = sortBy
      this.currentPage = 1
    },
    loadMore() {
      this.currentPage++
    },
    goToPolicyDetail(policy) {
      this.$router.push(`/policy/${policy.id}`)
    },
    toggleFavorite(policy) {
      policy.isFavorite = !policy.isFavorite
      // 实际项目中这里会调用API
    },
    sharePolicy(policy) {
      // 分享功能
      alert(`分享政策: ${policy.title}`)
    },
    resetFilters() {
      this.filters = {
        timeRange: 'all',
        targets: []
      }
    },
    applyFilters() {
      this.showFilterModal = false
      this.currentPage = 1
      // 应用筛选条件
    },
    getStatusText(status) {
      const statusMap = {
        'active': '有效',
        'expired': '已过期',
        'draft': '草案'
      }
      return statusMap[status] || '未知'
    },
    getTagClass(tag) {
      const tagMap = {
        '企业扶持': 'tag-primary',
        '数字化': 'tag-success',
        '个人税收': 'tag-warning',
        '减税': 'tag-success',
        '创业扶持': 'tag-primary',
        '高新技术': 'tag-success'
      }
      return tagMap[tag] || ''
    }
  },
  mounted() {
    // 从路由参数获取分类
    if (this.$route.query.category) {
      this.activeCategory = this.$route.query.category
    }
  }
}
</script>

<style scoped>
/* 搜索头部 */
.search-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 8px 16px;
}

.search-input-wrapper i.fa-search {
  color: #999;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 14px;
}

.clear-btn {
  color: #999;
  cursor: pointer;
  margin-left: 8px;
}

.search-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 16px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
}

/* 分类筛选 */
.category-filter {
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  overflow-x: auto;
  padding: 0 16px;
}

.filter-tab {
  flex-shrink: 0;
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.filter-tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

/* 排序筛选 */
.sort-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.sort-options {
  display: flex;
  gap: 16px;
}

.sort-option {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: color 0.3s;
}

.sort-option.active {
  color: #1890ff;
  font-weight: 500;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

/* 政策结果 */
.policy-results {
  padding: 16px;
}

.result-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.policy-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.policy-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.policy-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.policy-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
}

.policy-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
}

.policy-status.active {
  background: #f6ffed;
  color: #52c41a;
}

.policy-summary {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.policy-info {
  margin-bottom: 12px;
}

.policy-tags {
  margin-bottom: 8px;
}

.policy-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

.policy-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: 20px;
}

.load-more-btn {
  background: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.filter-content {
  background: white;
  border-radius: 12px 12px 0 0;
  padding: 20px;
  width: 100%;
  max-height: 70vh;
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-header h3 {
  font-size: 16px;
  font-weight: 500;
}

.filter-header i {
  font-size: 18px;
  color: #999;
  cursor: pointer;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-section h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-options label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  cursor: pointer;
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.filter-actions .btn {
  flex: 1;
  padding: 12px;
  text-align: center;
}
</style>
