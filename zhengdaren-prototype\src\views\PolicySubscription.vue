<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部导航 -->
      <div class="header">
        <i class="fas fa-arrow-left back-btn" @click="goBack"></i>
        <h2>政策订阅</h2>
        <div class="header-actions">
          <i class="fas fa-cog" @click="goToSettings"></i>
        </div>
      </div>

      <!-- 订阅统计 -->
      <div class="subscription-stats">
        <div class="stats-card">
          <div class="stat-item">
            <span class="stat-number">{{ subscriptionStats.totalSubscriptions }}</span>
            <span class="stat-label">订阅数量</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ subscriptionStats.todayPush }}</span>
            <span class="stat-label">今日推送</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ subscriptionStats.weeklyPush }}</span>
            <span class="stat-label">本周推送</span>
          </div>
        </div>
      </div>

      <!-- 快速订阅 -->
      <div class="quick-subscribe">
        <div class="section-header">
          <h4>快速订阅</h4>
          <span class="section-subtitle">选择您关注的政策类型</span>
        </div>
        <div class="quick-options">
          <div 
            class="quick-option" 
            :class="{ active: option.subscribed }"
            v-for="option in quickSubscribeOptions" 
            :key="option.id"
            @click="toggleQuickSubscribe(option)"
          >
            <div class="option-icon" :style="{ backgroundColor: option.color }">
              <i :class="option.icon"></i>
            </div>
            <div class="option-info">
              <h5>{{ option.name }}</h5>
              <p>{{ option.description }}</p>
            </div>
            <div class="option-toggle">
              <i :class="option.subscribed ? 'fas fa-check-circle' : 'far fa-circle'"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 我的订阅 -->
      <div class="my-subscriptions">
        <div class="section-header">
          <h4>我的订阅</h4>
          <button class="add-subscription-btn" @click="addSubscription">
            <i class="fas fa-plus"></i>
            添加订阅
          </button>
        </div>
        <div class="subscription-list">
          <div 
            class="subscription-item" 
            v-for="subscription in mySubscriptions" 
            :key="subscription.id"
            @click="viewSubscriptionDetail(subscription)"
          >
            <div class="subscription-header">
              <div class="subscription-info">
                <h5>{{ subscription.name }}</h5>
                <div class="subscription-tags">
                  <span class="tag" v-for="tag in subscription.keywords.slice(0, 3)" :key="tag">
                    {{ tag }}
                  </span>
                </div>
              </div>
              <div class="subscription-status" :class="{ active: subscription.isActive }">
                <i :class="subscription.isActive ? 'fas fa-bell' : 'fas fa-bell-slash'"></i>
              </div>
            </div>
            
            <div class="subscription-stats">
              <div class="stat">
                <span class="stat-label">创建时间</span>
                <span class="stat-value">{{ subscription.createdDate }}</span>
              </div>
              <div class="stat">
                <span class="stat-label">推送频率</span>
                <span class="stat-value">{{ getFrequencyText(subscription.frequency) }}</span>
              </div>
              <div class="stat">
                <span class="stat-label">匹配政策</span>
                <span class="stat-value">{{ subscription.matchedPolicies }}条</span>
              </div>
            </div>
            
            <div class="subscription-actions">
              <button 
                class="action-btn" 
                @click.stop="toggleSubscription(subscription)"
              >
                {{ subscription.isActive ? '暂停' : '启用' }}
              </button>
              <button 
                class="action-btn" 
                @click.stop="editSubscription(subscription)"
              >
                编辑
              </button>
              <button 
                class="action-btn danger" 
                @click.stop="deleteSubscription(subscription)"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 推送历史 -->
      <div class="push-history">
        <div class="section-header">
          <h4>推送历史</h4>
          <div class="history-filter">
            <select v-model="historyFilter">
              <option value="all">全部</option>
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
        </div>
        <div class="history-list">
          <div 
            class="history-item" 
            v-for="item in filteredHistory" 
            :key="item.id"
            @click="viewHistoryDetail(item)"
          >
            <div class="history-header">
              <div class="history-info">
                <h5>{{ item.title }}</h5>
                <p class="history-source">来源：{{ item.source }}</p>
              </div>
              <div class="history-time">{{ item.pushTime }}</div>
            </div>
            <div class="history-content">
              <p>{{ item.summary }}</p>
            </div>
            <div class="history-meta">
              <div class="history-tags">
                <span class="tag" v-for="tag in item.tags" :key="tag">{{ tag }}</span>
              </div>
              <div class="history-actions">
                <button class="meta-btn" @click.stop="markAsRead(item)" v-if="!item.isRead">
                  <i class="fas fa-check"></i>
                  标记已读
                </button>
                <button class="meta-btn" @click.stop="shareHistory(item)">
                  <i class="fas fa-share"></i>
                  分享
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 推送设置 -->
      <div class="push-settings">
        <h4>推送设置</h4>
        <div class="settings-list">
          <div class="setting-item">
            <div class="setting-info">
              <h5>推送时间</h5>
              <p>设置接收政策推送的时间段</p>
            </div>
            <div class="setting-control">
              <select v-model="pushSettings.timeRange">
                <option value="anytime">任何时间</option>
                <option value="business">工作时间(9:00-18:00)</option>
                <option value="evening">晚间时间(18:00-22:00)</option>
                <option value="custom">自定义时间</option>
              </select>
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <h5>推送方式</h5>
              <p>选择接收政策推送的方式</p>
            </div>
            <div class="setting-control">
              <div class="checkbox-group">
                <label class="checkbox-item">
                  <input type="checkbox" v-model="pushSettings.methods" value="app">
                  <span>App推送</span>
                </label>
                <label class="checkbox-item">
                  <input type="checkbox" v-model="pushSettings.methods" value="email">
                  <span>邮件推送</span>
                </label>
                <label class="checkbox-item">
                  <input type="checkbox" v-model="pushSettings.methods" value="sms">
                  <span>短信推送</span>
                </label>
              </div>
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <h5>推送频率限制</h5>
              <p>避免过于频繁的推送打扰</p>
            </div>
            <div class="setting-control">
              <select v-model="pushSettings.frequency">
                <option value="realtime">实时推送</option>
                <option value="hourly">每小时汇总</option>
                <option value="daily">每日汇总</option>
                <option value="weekly">每周汇总</option>
              </select>
            </div>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <h5>智能过滤</h5>
              <p>AI智能过滤低相关度的政策推送</p>
            </div>
            <div class="setting-control">
              <label class="switch">
                <input type="checkbox" v-model="pushSettings.smartFilter">
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </div>
        
        <button class="save-settings-btn" @click="saveSettings">
          <i class="fas fa-save"></i>
          保存设置
        </button>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'PolicySubscription',
  components: {
    BottomNavigation
  },
  data() {
    return {
      historyFilter: 'all',
      subscriptionStats: {
        totalSubscriptions: 8,
        todayPush: 12,
        weeklyPush: 45
      },
      quickSubscribeOptions: [
        {
          id: 1,
          name: '制造业政策',
          description: '智能制造、数字化转型等制造业相关政策',
          icon: 'fas fa-industry',
          color: '#1890ff',
          subscribed: true
        },
        {
          id: 2,
          name: '税收优惠',
          description: '各类税收减免、优惠政策更新',
          icon: 'fas fa-percentage',
          color: '#52c41a',
          subscribed: true
        },
        {
          id: 3,
          name: '创业扶持',
          description: '创业补贴、孵化器、投资政策',
          icon: 'fas fa-rocket',
          color: '#fa8c16',
          subscribed: false
        },
        {
          id: 4,
          name: '人才政策',
          description: '人才引进、培训补贴、技能提升',
          icon: 'fas fa-user-graduate',
          color: '#722ed1',
          subscribed: false
        }
      ],
      mySubscriptions: [
        {
          id: 1,
          name: '制造业数字化转型政策监控',
          keywords: ['数字化转型', '智能制造', '工业4.0', '自动化'],
          isActive: true,
          frequency: 'daily',
          createdDate: '2024-01-15',
          matchedPolicies: 23
        },
        {
          id: 2,
          name: '中小企业税收优惠',
          keywords: ['税收优惠', '中小企业', '减税', '免税'],
          isActive: true,
          frequency: 'weekly',
          createdDate: '2024-01-10',
          matchedPolicies: 15
        },
        {
          id: 3,
          name: '高新技术企业认定',
          keywords: ['高新技术', '企业认定', '研发费用', '知识产权'],
          isActive: false,
          frequency: 'monthly',
          createdDate: '2024-01-05',
          matchedPolicies: 8
        }
      ],
      pushHistory: [
        {
          id: 1,
          title: '制造业数字化转型专项资金申报通知',
          summary: '最高补贴500万元，申请截止时间为3月31日，符合条件的制造业企业可申请...',
          source: '制造业数字化转型政策监控',
          pushTime: '2小时前',
          tags: ['数字化转型', '专项资金', '制造业'],
          isRead: false,
          date: '2024-01-25'
        },
        {
          id: 2,
          title: '小微企业所得税优惠政策延续',
          summary: '小微企业所得税优惠政策将延续至2024年底，年应纳税所得额不超过100万元...',
          source: '中小企业税收优惠',
          pushTime: '5小时前',
          tags: ['税收优惠', '小微企业', '所得税'],
          isRead: true,
          date: '2024-01-25'
        },
        {
          id: 3,
          title: '高新技术企业认定管理办法修订',
          summary: '新修订的高新技术企业认定管理办法将于4月1日起施行，主要变化包括...',
          source: '高新技术企业认定',
          pushTime: '1天前',
          tags: ['高新技术', '企业认定', '管理办法'],
          isRead: true,
          date: '2024-01-24'
        },
        {
          id: 4,
          title: '创业担保贷款政策优化调整',
          summary: '个人创业担保贷款最高额度提升至20万元，小微企业最高300万元...',
          source: '创业扶持政策',
          pushTime: '2天前',
          tags: ['创业担保', '贷款政策', '小微企业'],
          isRead: true,
          date: '2024-01-23'
        }
      ],
      pushSettings: {
        timeRange: 'business',
        methods: ['app', 'email'],
        frequency: 'daily',
        smartFilter: true
      }
    }
  },
  computed: {
    filteredHistory() {
      if (this.historyFilter === 'all') {
        return this.pushHistory
      }
      
      const now = new Date()
      const today = now.toISOString().split('T')[0]
      
      return this.pushHistory.filter(item => {
        const itemDate = new Date(item.date)
        
        switch (this.historyFilter) {
          case 'today':
            return item.date === today
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            return itemDate >= weekAgo
          case 'month':
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
            return itemDate >= monthAgo
          default:
            return true
        }
      })
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToSettings() {
      this.$router.push('/subscription-settings')
    },
    toggleQuickSubscribe(option) {
      option.subscribed = !option.subscribed
      // 实际项目中这里会调用API
    },
    addSubscription() {
      this.$router.push('/add-subscription')
    },
    viewSubscriptionDetail(subscription) {
      this.$router.push(`/subscription/${subscription.id}`)
    },
    toggleSubscription(subscription) {
      subscription.isActive = !subscription.isActive
      // 实际项目中这里会调用API
    },
    editSubscription(subscription) {
      this.$router.push(`/edit-subscription/${subscription.id}`)
    },
    deleteSubscription(subscription) {
      if (confirm(`确定要删除订阅"${subscription.name}"吗？`)) {
        const index = this.mySubscriptions.findIndex(s => s.id === subscription.id)
        if (index > -1) {
          this.mySubscriptions.splice(index, 1)
        }
      }
    },
    viewHistoryDetail(item) {
      this.$router.push(`/policy-push/${item.id}`)
    },
    markAsRead(item) {
      item.isRead = true
      // 实际项目中这里会调用API
    },
    shareHistory(item) {
      if (navigator.share) {
        navigator.share({
          title: item.title,
          text: item.summary,
          url: window.location.href
        })
      } else {
        alert('已复制分享链接')
      }
    },
    getFrequencyText(frequency) {
      const frequencyMap = {
        'realtime': '实时',
        'hourly': '每小时',
        'daily': '每日',
        'weekly': '每周',
        'monthly': '每月'
      }
      return frequencyMap[frequency] || '未知'
    },
    saveSettings() {
      // 保存推送设置
      alert('设置已保存')
    }
  }
}
</script>

<style scoped>
/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* 订阅统计 */
.subscription-stats {
  padding: 16px;
  background: #f5f5f5;
}

.stats-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* 快速订阅 */
.quick-subscribe {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.section-header {
  margin-bottom: 16px;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.section-subtitle {
  font-size: 13px;
  color: #666;
}

.quick-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-option:hover {
  border-color: #1890ff;
}

.quick-option.active {
  border-color: #1890ff;
  background: #f0f8ff;
}

.option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.option-icon i {
  font-size: 18px;
  color: white;
}

.option-info {
  flex: 1;
}

.option-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.option-info p {
  font-size: 12px;
  color: #666;
}

.option-toggle {
  font-size: 20px;
  color: #1890ff;
}

/* 我的订阅 */
.my-subscriptions {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.add-subscription-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
}

.subscription-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.subscription-item {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.subscription-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.subscription-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.subscription-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.subscription-tags .tag {
  background: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.subscription-status {
  font-size: 18px;
  color: #d9d9d9;
}

.subscription-status.active {
  color: #52c41a;
}

.subscription-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 11px;
  color: #999;
}

.stat-value {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.subscription-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.action-btn.danger:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* 推送历史 */
.push-history {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.history-filter select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background: #f0f0f0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.history-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.history-source {
  font-size: 12px;
  color: #999;
}

.history-time {
  font-size: 12px;
  color: #999;
}

.history-content {
  margin-bottom: 12px;
}

.history-content p {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.history-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.history-tags .tag {
  background: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.history-actions {
  display: flex;
  gap: 8px;
}

.meta-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #666;
  font-size: 11px;
  cursor: pointer;
  padding: 2px 4px;
}

.meta-btn:hover {
  color: #1890ff;
}

/* 推送设置 */
.push-settings {
  padding: 16px;
  background: white;
}

.push-settings h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
}

.setting-info {
  flex: 1;
  margin-right: 16px;
}

.setting-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.setting-info p {
  font-size: 12px;
  color: #666;
}

.setting-control select {
  padding: 6px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 13px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  cursor: pointer;
}

.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #1890ff;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.save-settings-btn {
  width: 100%;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.save-settings-btn:hover {
  background: #40a9ff;
}
</style>
