<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部导航 -->
      <div class="header">
        <i class="fas fa-arrow-left back-btn" @click="goBack"></i>
        <h2>会员服务</h2>
        <div class="header-actions">
          <i class="fas fa-question-circle" @click="showHelp"></i>
        </div>
      </div>

      <!-- 当前会员状态 -->
      <div class="current-membership" v-if="userMembership">
        <div class="membership-card" :class="userMembership.type">
          <div class="card-header">
            <div class="membership-info">
              <div class="membership-icon">
                <i :class="getMembershipIcon(userMembership.type)"></i>
              </div>
              <div class="membership-details">
                <h3>{{ getMembershipName(userMembership.type) }}</h3>
                <p v-if="userMembership.expireDate">到期时间：{{ userMembership.expireDate }}</p>
                <p v-else>永久有效</p>
              </div>
            </div>
            <div class="membership-badge">
              {{ getMembershipBadge(userMembership.type) }}
            </div>
          </div>
          
          <div class="usage-stats">
            <div class="stat-item">
              <span class="stat-number">{{ userMembership.usedQuota }}</span>
              <span class="stat-label">已使用</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ userMembership.totalQuota }}</span>
              <span class="stat-label">总额度</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ userMembership.savedAmount }}</span>
              <span class="stat-label">已节省</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 会员套餐选择 -->
      <div class="membership-plans">
        <h3 class="section-title">选择适合您的套餐</h3>
        
        <div class="plan-selector">
          <div class="billing-toggle">
            <span :class="{ active: billingCycle === 'monthly' }" @click="billingCycle = 'monthly'">月付</span>
            <span :class="{ active: billingCycle === 'yearly' }" @click="billingCycle = 'yearly'">年付</span>
            <div class="discount-badge" v-if="billingCycle === 'yearly'">省20%</div>
          </div>
        </div>

        <div class="plans-grid">
          <div 
            class="plan-card" 
            :class="{ 
              recommended: plan.isRecommended,
              current: userMembership && userMembership.type === plan.type
            }"
            v-for="plan in membershipPlans" 
            :key="plan.type"
          >
            <div class="plan-header">
              <div class="plan-icon">
                <i :class="plan.icon"></i>
              </div>
              <h4 class="plan-name">{{ plan.name }}</h4>
              <div class="plan-badge" v-if="plan.isRecommended">推荐</div>
              <div class="plan-badge current" v-if="userMembership && userMembership.type === plan.type">当前</div>
            </div>
            
            <div class="plan-price">
              <span class="price-symbol">¥</span>
              <span class="price-amount">{{ getPlanPrice(plan) }}</span>
              <span class="price-period">/{{ billingCycle === 'monthly' ? '月' : '年' }}</span>
            </div>
            
            <div class="plan-description">{{ plan.description }}</div>
            
            <div class="plan-features">
              <div 
                class="feature-item" 
                :class="{ disabled: !feature.included }"
                v-for="feature in plan.features" 
                :key="feature.name"
              >
                <i :class="feature.included ? 'fas fa-check' : 'fas fa-times'"></i>
                <span>{{ feature.name }}</span>
                <span class="feature-limit" v-if="feature.limit">{{ feature.limit }}</span>
              </div>
            </div>
            
            <button 
              class="plan-button" 
              :class="plan.type"
              :disabled="userMembership && userMembership.type === plan.type"
              @click="selectPlan(plan)"
            >
              {{ getPlanButtonText(plan) }}
            </button>
          </div>
        </div>
      </div>

      <!-- 会员权益详情 -->
      <div class="membership-benefits">
        <h3 class="section-title">会员专享权益</h3>
        <div class="benefits-grid">
          <div class="benefit-item" v-for="benefit in membershipBenefits" :key="benefit.id">
            <div class="benefit-icon" :style="{ backgroundColor: benefit.color }">
              <i :class="benefit.icon"></i>
            </div>
            <div class="benefit-info">
              <h4>{{ benefit.name }}</h4>
              <p>{{ benefit.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 常见问题 -->
      <div class="faq-section">
        <h3 class="section-title">常见问题</h3>
        <div class="faq-list">
          <div 
            class="faq-item" 
            v-for="faq in faqs" 
            :key="faq.id"
            @click="toggleFaq(faq)"
          >
            <div class="faq-question">
              <span>{{ faq.question }}</span>
              <i :class="faq.expanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
            </div>
            <div class="faq-answer" v-if="faq.expanded">
              {{ faq.answer }}
            </div>
          </div>
        </div>
      </div>

      <!-- 客服联系 -->
      <div class="customer-service">
        <div class="service-card">
          <div class="service-info">
            <h4>需要帮助？</h4>
            <p>专业客服团队为您提供7×24小时服务</p>
          </div>
          <button class="contact-btn" @click="contactService">
            <i class="fas fa-headset"></i>
            联系客服
          </button>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'Membership',
  components: {
    BottomNavigation
  },
  data() {
    return {
      billingCycle: 'monthly', // monthly, yearly
      userMembership: {
        type: 'free',
        expireDate: null,
        usedQuota: 15,
        totalQuota: 50,
        savedAmount: '0'
      },
      membershipPlans: [
        {
          type: 'free',
          name: '免费版',
          icon: 'fas fa-user',
          description: '适合个人用户基础使用',
          monthlyPrice: 0,
          yearlyPrice: 0,
          isRecommended: false,
          features: [
            { name: '政策浏览', included: true, limit: '每日10条' },
            { name: '基础搜索', included: true },
            { name: 'AI问答', included: true, limit: '每日5次' },
            { name: '政策收藏', included: true, limit: '最多50条' },
            { name: '政策订阅', included: false },
            { name: '专家咨询', included: false },
            { name: '高级工具', included: false },
            { name: '专属客服', included: false }
          ]
        },
        {
          type: 'professional',
          name: '专业版',
          icon: 'fas fa-star',
          description: '适合专业人士和小企业',
          monthlyPrice: 99,
          yearlyPrice: 950,
          isRecommended: true,
          features: [
            { name: '政策浏览', included: true, limit: '无限制' },
            { name: '高级搜索', included: true },
            { name: 'AI深度解读', included: true, limit: '每日20次' },
            { name: '政策收藏', included: true, limit: '无限制' },
            { name: '政策订阅推送', included: true },
            { name: '专属政策报告', included: true, limit: '月报' },
            { name: '在线客服', included: true },
            { name: '专家咨询', included: false }
          ]
        },
        {
          type: 'enterprise',
          name: '企业版',
          icon: 'fas fa-building',
          description: '适合中小企业团队使用',
          monthlyPrice: 999,
          yearlyPrice: 9590,
          isRecommended: false,
          features: [
            { name: '专业版全部功能', included: true },
            { name: '企业政策画像', included: true },
            { name: '专家咨询', included: true, limit: '2小时/月' },
            { name: '政策申请指导', included: true },
            { name: '定制化监控', included: true },
            { name: '团队协作', included: true, limit: '5人' },
            { name: '优先客服', included: true },
            { name: '政策培训', included: false }
          ]
        },
        {
          type: 'flagship',
          name: '旗舰版',
          icon: 'fas fa-crown',
          description: '适合大型企业和机构',
          monthlyPrice: 2999,
          yearlyPrice: 28790,
          isRecommended: false,
          features: [
            { name: '企业版全部功能', included: true },
            { name: '无限专家咨询', included: true },
            { name: '政策申请代办', included: true },
            { name: '行业趋势分析', included: true },
            { name: '政策风险预警', included: true },
            { name: '专属客户经理', included: true },
            { name: '定制化培训', included: true },
            { name: 'API接口', included: true }
          ]
        }
      ],
      membershipBenefits: [
        {
          id: 1,
          name: 'AI智能解读',
          description: '专业AI深度解读政策条文，提取关键信息',
          icon: 'fas fa-robot',
          color: '#1890ff'
        },
        {
          id: 2,
          name: '专家咨询服务',
          description: '一对一专家咨询，解答政策疑问',
          icon: 'fas fa-user-tie',
          color: '#52c41a'
        },
        {
          id: 3,
          name: '政策订阅推送',
          description: '个性化政策推送，不错过任何机会',
          icon: 'fas fa-bell',
          color: '#fa8c16'
        },
        {
          id: 4,
          name: '申请全程指导',
          description: '从准备到提交的全流程专业指导',
          icon: 'fas fa-clipboard-list',
          color: '#722ed1'
        },
        {
          id: 5,
          name: '企业政策画像',
          description: '基于企业特征精准匹配政策资源',
          icon: 'fas fa-chart-pie',
          color: '#13c2c2'
        },
        {
          id: 6,
          name: '优先客服支持',
          description: '7×24小时专属客服，优先响应',
          icon: 'fas fa-headset',
          color: '#eb2f96'
        }
      ],
      faqs: [
        {
          id: 1,
          question: '如何选择适合的会员套餐？',
          answer: '根据您的使用频率和需求选择：个人用户推荐专业版，企业用户推荐企业版或旗舰版。',
          expanded: false
        },
        {
          id: 2,
          question: '会员费用如何计算？',
          answer: '按月或按年付费，年付可享受8折优惠。支持多种支付方式。',
          expanded: false
        },
        {
          id: 3,
          question: '可以随时取消会员吗？',
          answer: '可以随时取消，取消后仍可使用至当前付费周期结束。',
          expanded: false
        },
        {
          id: 4,
          question: '专家咨询如何预约？',
          answer: '会员用户可在专家咨询页面选择专家和时间进行预约，系统会自动安排。',
          expanded: false
        }
      ]
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    showHelp() {
      // 显示帮助信息
      alert('会员服务帮助')
    },
    getMembershipIcon(type) {
      const iconMap = {
        'free': 'fas fa-user',
        'professional': 'fas fa-star',
        'enterprise': 'fas fa-building',
        'flagship': 'fas fa-crown'
      }
      return iconMap[type] || 'fas fa-user'
    },
    getMembershipName(type) {
      const nameMap = {
        'free': '免费版',
        'professional': '专业版',
        'enterprise': '企业版',
        'flagship': '旗舰版'
      }
      return nameMap[type] || '未知'
    },
    getMembershipBadge(type) {
      const badgeMap = {
        'free': 'FREE',
        'professional': 'PRO',
        'enterprise': 'ENT',
        'flagship': 'VIP'
      }
      return badgeMap[type] || ''
    },
    getPlanPrice(plan) {
      return this.billingCycle === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice
    },
    getPlanButtonText(plan) {
      if (this.userMembership && this.userMembership.type === plan.type) {
        return '当前套餐'
      }
      if (plan.type === 'free') {
        return '免费使用'
      }
      return '立即开通'
    },
    selectPlan(plan) {
      if (this.userMembership && this.userMembership.type === plan.type) {
        return
      }
      
      if (plan.type === 'free') {
        // 切换到免费版
        this.userMembership = {
          type: 'free',
          expireDate: null,
          usedQuota: 0,
          totalQuota: 50,
          savedAmount: '0'
        }
        alert('已切换到免费版')
      } else {
        // 跳转到支付页面
        this.$router.push(`/payment?plan=${plan.type}&cycle=${this.billingCycle}`)
      }
    },
    toggleFaq(faq) {
      faq.expanded = !faq.expanded
    },
    contactService() {
      this.$router.push('/customer-service')
    }
  }
}
</script>

<style scoped>
/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* 当前会员状态 */
.current-membership {
  padding: 16px;
  background: #f5f5f5;
}

.membership-card {
  border-radius: 12px;
  padding: 20px;
  color: white;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.membership-card.professional {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.membership-card.enterprise {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.membership-card.flagship {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.membership-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.membership-icon {
  width: 40px;
  height: 40px;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.membership-icon i {
  font-size: 20px;
}

.membership-details h3 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 4px;
}

.membership-details p {
  font-size: 14px;
  opacity: 0.9;
}

.membership-badge {
  background: rgba(255,255,255,0.2);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.usage-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

/* 会员套餐 */
.membership-plans {
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
}

.plan-selector {
  margin-bottom: 20px;
}

.billing-toggle {
  display: flex;
  background: #f0f0f0;
  border-radius: 20px;
  padding: 4px;
  position: relative;
  width: fit-content;
  margin: 0 auto;
}

.billing-toggle span {
  padding: 8px 20px;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.billing-toggle span.active {
  background: white;
  color: #1890ff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.discount-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

.plans-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.plan-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #f0f0f0;
  transition: all 0.3s;
  position: relative;
}

.plan-card.recommended {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24,144,255,0.2);
}

.plan-card.current {
  border-color: #52c41a;
  background: #f6ffed;
}

.plan-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  position: relative;
}

.plan-icon {
  width: 40px;
  height: 40px;
  background: #f0f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plan-icon i {
  font-size: 18px;
  color: #1890ff;
}

.plan-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.plan-badge {
  background: #1890ff;
  color: white;
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 8px;
}

.plan-badge.current {
  background: #52c41a;
}

.plan-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.price-symbol {
  font-size: 16px;
  color: #666;
}

.price-amount {
  font-size: 28px;
  font-weight: 600;
  color: #333;
}

.price-period {
  font-size: 14px;
  color: #666;
  margin-left: 4px;
}

.plan-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.plan-features {
  margin-bottom: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.feature-item.disabled {
  color: #ccc;
}

.feature-item i {
  width: 16px;
  font-size: 12px;
}

.feature-item i.fa-check {
  color: #52c41a;
}

.feature-item i.fa-times {
  color: #ccc;
}

.feature-limit {
  margin-left: auto;
  font-size: 12px;
  color: #999;
}

.plan-button {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.plan-button.free {
  background: #f0f0f0;
  color: #666;
}

.plan-button.professional {
  background: #1890ff;
  color: white;
}

.plan-button.enterprise {
  background: #722ed1;
  color: white;
}

.plan-button.flagship {
  background: #fa8c16;
  color: white;
}

.plan-button:disabled {
  background: #f0f0f0;
  color: #ccc;
  cursor: not-allowed;
}

/* 会员权益 */
.membership-benefits {
  padding: 16px;
  background: #f9f9f9;
}

.benefits-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.benefit-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.benefit-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.benefit-icon i {
  font-size: 18px;
  color: white;
}

.benefit-info h4 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.benefit-info p {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 常见问题 */
.faq-section {
  padding: 16px;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.faq-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.faq-question i {
  color: #999;
  font-size: 12px;
}

.faq-answer {
  padding: 0 16px 16px 16px;
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  border-top: 1px solid #f0f0f0;
}

/* 客服联系 */
.customer-service {
  padding: 16px;
}

.service-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-info h4 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.service-info p {
  font-size: 12px;
  color: #666;
}

.contact-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
}
</style>
