<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部导航 -->
      <div class="header">
        <i class="fas fa-arrow-left back-btn" @click="goBack"></i>
        <h2>专家咨询</h2>
        <div class="header-actions">
          <i class="fas fa-history" @click="viewHistory"></i>
        </div>
      </div>

      <!-- 服务介绍 -->
      <div class="service-intro">
        <div class="intro-card">
          <div class="intro-icon">
            <i class="fas fa-user-tie"></i>
          </div>
          <div class="intro-content">
            <h3>专业政策咨询服务</h3>
            <p>资深政策专家一对一咨询，为您提供专业的政策解读和申请指导</p>
          </div>
          <div class="intro-badge">
            <span>7×24小时</span>
          </div>
        </div>
      </div>

      <!-- 咨询统计 -->
      <div class="consultation-stats" v-if="userStats">
        <div class="stats-card">
          <div class="stat-item">
            <span class="stat-number">{{ userStats.totalConsultations }}</span>
            <span class="stat-label">累计咨询</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ userStats.remainingHours }}</span>
            <span class="stat-label">剩余时长(小时)</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ userStats.successRate }}%</span>
            <span class="stat-label">申请成功率</span>
          </div>
        </div>
      </div>

      <!-- 专家筛选 -->
      <div class="expert-filter">
        <div class="filter-tabs">
          <div 
            class="filter-tab" 
            :class="{ active: activeFilter === filter.key }"
            v-for="filter in expertFilters" 
            :key="filter.key"
            @click="selectFilter(filter.key)"
          >
            {{ filter.name }}
          </div>
        </div>
      </div>

      <!-- 专家列表 -->
      <div class="expert-list">
        <div 
          class="expert-card" 
          v-for="expert in filteredExperts" 
          :key="expert.id"
          @click="viewExpertDetail(expert)"
        >
          <div class="expert-header">
            <div class="expert-avatar">
              <img v-if="expert.avatar" :src="expert.avatar" :alt="expert.name">
              <i v-else class="fas fa-user-circle"></i>
              <div class="online-status" :class="{ online: expert.isOnline }"></div>
            </div>
            <div class="expert-info">
              <h4>{{ expert.name }}</h4>
              <p class="expert-title">{{ expert.title }}</p>
              <div class="expert-tags">
                <span class="tag" v-for="tag in expert.specialties.slice(0, 2)" :key="tag">
                  {{ tag }}
                </span>
              </div>
            </div>
            <div class="expert-rating">
              <div class="rating-stars">
                <i 
                  class="fas fa-star" 
                  v-for="n in 5" 
                  :key="n"
                  :class="{ active: n <= expert.rating }"
                ></i>
              </div>
              <span class="rating-score">{{ expert.rating }}</span>
            </div>
          </div>
          
          <div class="expert-stats">
            <div class="stat">
              <span class="stat-value">{{ expert.experience }}年</span>
              <span class="stat-label">从业经验</span>
            </div>
            <div class="stat">
              <span class="stat-value">{{ expert.consultations }}</span>
              <span class="stat-label">咨询次数</span>
            </div>
            <div class="stat">
              <span class="stat-value">{{ expert.successRate }}%</span>
              <span class="stat-label">成功率</span>
            </div>
          </div>
          
          <div class="expert-description">
            <p>{{ expert.description }}</p>
          </div>
          
          <div class="expert-actions">
            <div class="pricing-info">
              <span class="price">¥{{ expert.hourlyRate }}/小时</span>
              <span class="response-time">{{ expert.responseTime }}内回复</span>
            </div>
            <div class="action-buttons">
              <button 
                class="btn btn-secondary" 
                @click.stop="sendMessage(expert)"
                :disabled="!expert.isOnline"
              >
                <i class="fas fa-comment"></i>
                {{ expert.isOnline ? '立即咨询' : '离线留言' }}
              </button>
              <button 
                class="btn btn-primary" 
                @click.stop="bookConsultation(expert)"
              >
                <i class="fas fa-calendar"></i>
                预约咨询
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速咨询 -->
      <div class="quick-consultation">
        <div class="quick-header">
          <h4>快速咨询</h4>
          <span class="quick-subtitle">描述您的问题，系统将为您匹配最合适的专家</span>
        </div>
        <div class="quick-form">
          <div class="form-group">
            <label>咨询类型</label>
            <select v-model="quickConsult.type">
              <option value="">请选择咨询类型</option>
              <option value="policy-interpretation">政策解读</option>
              <option value="application-guidance">申请指导</option>
              <option value="risk-assessment">风险评估</option>
              <option value="compliance-check">合规检查</option>
            </select>
          </div>
          <div class="form-group">
            <label>问题描述</label>
            <textarea 
              v-model="quickConsult.description" 
              placeholder="请详细描述您的问题，以便专家更好地为您服务"
              rows="4"
            ></textarea>
          </div>
          <div class="form-group">
            <label>紧急程度</label>
            <div class="urgency-options">
              <label class="radio-option" v-for="option in urgencyOptions" :key="option.value">
                <input type="radio" v-model="quickConsult.urgency" :value="option.value">
                <span class="radio-label">{{ option.label }}</span>
                <span class="radio-desc">{{ option.description }}</span>
              </label>
            </div>
          </div>
          <button 
            class="submit-btn" 
            @click="submitQuickConsult"
            :disabled="!canSubmitQuickConsult"
          >
            <i class="fas fa-paper-plane"></i>
            提交咨询
          </button>
        </div>
      </div>

      <!-- 咨询记录 -->
      <div class="consultation-history" v-if="recentConsultations.length > 0">
        <div class="section-header">
          <h4>最近咨询</h4>
          <span class="more-link" @click="viewAllHistory">查看全部</span>
        </div>
        <div class="history-list">
          <div 
            class="history-item" 
            v-for="consultation in recentConsultations" 
            :key="consultation.id"
            @click="viewConsultationDetail(consultation)"
          >
            <div class="history-header">
              <div class="consultation-info">
                <h5>{{ consultation.title }}</h5>
                <p class="expert-name">专家：{{ consultation.expertName }}</p>
              </div>
              <div class="consultation-status" :class="consultation.status">
                {{ getStatusText(consultation.status) }}
              </div>
            </div>
            <div class="consultation-meta">
              <span class="consultation-date">{{ consultation.date }}</span>
              <span class="consultation-duration">{{ consultation.duration }}分钟</span>
              <span class="consultation-rating" v-if="consultation.rating">
                <i class="fas fa-star"></i>
                {{ consultation.rating }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'ExpertConsultation',
  components: {
    BottomNavigation
  },
  data() {
    return {
      activeFilter: 'all',
      userStats: {
        totalConsultations: 12,
        remainingHours: 8,
        successRate: 85
      },
      quickConsult: {
        type: '',
        description: '',
        urgency: 'normal'
      },
      expertFilters: [
        { key: 'all', name: '全部' },
        { key: 'online', name: '在线' },
        { key: 'policy', name: '政策解读' },
        { key: 'application', name: '申请指导' },
        { key: 'compliance', name: '合规咨询' }
      ],
      urgencyOptions: [
        { value: 'low', label: '一般', description: '3天内回复' },
        { value: 'normal', label: '紧急', description: '24小时内回复' },
        { value: 'high', label: '非常紧急', description: '2小时内回复' }
      ],
      experts: [
        {
          id: 1,
          name: '张政策',
          title: '高级政策分析师',
          avatar: '',
          isOnline: true,
          rating: 4.9,
          experience: 15,
          consultations: 1280,
          successRate: 92,
          hourlyRate: 299,
          responseTime: '5分钟',
          specialties: ['制造业政策', '数字化转型', '税收优惠', '企业认定'],
          description: '15年政策研究经验，专注制造业政策解读，曾参与多项国家级政策制定工作。',
          category: 'policy'
        },
        {
          id: 2,
          name: '李申请',
          title: '资深申请顾问',
          avatar: '',
          isOnline: true,
          rating: 4.8,
          experience: 12,
          consultations: 980,
          successRate: 88,
          hourlyRate: 259,
          responseTime: '10分钟',
          specialties: ['申请指导', '材料准备', '流程优化', '风险控制'],
          description: '专业申请顾问，擅长各类政策申请流程设计和材料准备指导。',
          category: 'application'
        },
        {
          id: 3,
          name: '王合规',
          title: '合规风控专家',
          avatar: '',
          isOnline: false,
          rating: 4.7,
          experience: 18,
          consultations: 756,
          successRate: 95,
          hourlyRate: 399,
          responseTime: '30分钟',
          specialties: ['合规检查', '风险评估', '法律咨询', '审计支持'],
          description: '资深合规专家，具有丰富的企业合规管理和风险控制经验。',
          category: 'compliance'
        },
        {
          id: 4,
          name: '陈创新',
          title: '科技政策专家',
          avatar: '',
          isOnline: true,
          rating: 4.6,
          experience: 10,
          consultations: 654,
          successRate: 90,
          hourlyRate: 199,
          responseTime: '15分钟',
          specialties: ['科技创新', '研发补贴', '知识产权', '高新认定'],
          description: '科技政策专家，专注科技创新领域政策研究和申请指导。',
          category: 'policy'
        }
      ],
      recentConsultations: [
        {
          id: 1,
          title: '制造业数字化转型政策咨询',
          expertName: '张政策',
          status: 'completed',
          date: '2024-01-20',
          duration: 45,
          rating: 4.9
        },
        {
          id: 2,
          title: '高新技术企业认定申请指导',
          expertName: '陈创新',
          status: 'in-progress',
          date: '2024-01-18',
          duration: 30,
          rating: null
        },
        {
          id: 3,
          title: '税收优惠政策解读',
          expertName: '张政策',
          status: 'completed',
          date: '2024-01-15',
          duration: 60,
          rating: 4.8
        }
      ]
    }
  },
  computed: {
    filteredExperts() {
      if (this.activeFilter === 'all') {
        return this.experts
      } else if (this.activeFilter === 'online') {
        return this.experts.filter(expert => expert.isOnline)
      } else {
        return this.experts.filter(expert => expert.category === this.activeFilter)
      }
    },
    canSubmitQuickConsult() {
      return this.quickConsult.type && 
             this.quickConsult.description && 
             this.quickConsult.urgency
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    viewHistory() {
      this.$router.push('/consultation-history')
    },
    selectFilter(filterKey) {
      this.activeFilter = filterKey
    },
    viewExpertDetail(expert) {
      this.$router.push(`/expert/${expert.id}`)
    },
    sendMessage(expert) {
      if (expert.isOnline) {
        this.$router.push(`/chat/${expert.id}`)
      } else {
        this.$router.push(`/leave-message/${expert.id}`)
      }
    },
    bookConsultation(expert) {
      this.$router.push(`/book-consultation/${expert.id}`)
    },
    submitQuickConsult() {
      // 提交快速咨询
      alert('咨询已提交，系统正在为您匹配合适的专家')
      // 重置表单
      this.quickConsult = {
        type: '',
        description: '',
        urgency: 'normal'
      }
    },
    viewAllHistory() {
      this.$router.push('/consultation-history')
    },
    viewConsultationDetail(consultation) {
      this.$router.push(`/consultation/${consultation.id}`)
    },
    getStatusText(status) {
      const statusMap = {
        'completed': '已完成',
        'in-progress': '进行中',
        'pending': '待开始',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* 服务介绍 */
.service-intro {
  padding: 16px;
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.intro-card {
  display: flex;
  align-items: center;
  gap: 16px;
  color: white;
}

.intro-icon {
  width: 50px;
  height: 50px;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.intro-icon i {
  font-size: 24px;
}

.intro-content {
  flex: 1;
}

.intro-content h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.intro-content p {
  font-size: 13px;
  opacity: 0.9;
}

.intro-badge {
  background: rgba(255,255,255,0.2);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 咨询统计 */
.consultation-stats {
  padding: 16px;
  background: #f5f5f5;
}

.stats-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #52c41a;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* 专家筛选 */
.expert-filter {
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  overflow-x: auto;
  padding: 0 16px;
}

.filter-tab {
  flex-shrink: 0;
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.filter-tab.active {
  color: #52c41a;
  border-bottom-color: #52c41a;
}

/* 专家列表 */
.expert-list {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.expert-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
  border: 1px solid #f0f0f0;
}

.expert-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.expert-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.expert-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.expert-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.expert-avatar i {
  font-size: 50px;
  color: #d9d9d9;
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  background: #d9d9d9;
}

.online-status.online {
  background: #52c41a;
}

.expert-info {
  flex: 1;
}

.expert-info h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.expert-title {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}

.expert-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.expert-tags .tag {
  background: #f0f8ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.expert-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.rating-stars i {
  font-size: 12px;
  color: #d9d9d9;
}

.rating-stars i.active {
  color: #faad14;
}

.rating-score {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.expert-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 12px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 6px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.stat-label {
  font-size: 11px;
  color: #999;
}

.expert-description {
  margin-bottom: 16px;
}

.expert-description p {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.expert-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pricing-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.price {
  font-size: 16px;
  font-weight: 600;
  color: #52c41a;
}

.response-time {
  font-size: 11px;
  color: #999;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s;
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
}

.btn-secondary:hover {
  background: #e0e0e0;
}

.btn-primary {
  background: #52c41a;
  color: white;
}

.btn-primary:hover {
  background: #73d13d;
}

.btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

/* 快速咨询 */
.quick-consultation {
  padding: 16px;
  background: white;
  margin-top: 8px;
}

.quick-header {
  margin-bottom: 16px;
}

.quick-header h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.quick-subtitle {
  font-size: 13px;
  color: #666;
}

.quick-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
}

.form-group select:focus,
.form-group textarea:focus {
  border-color: #52c41a;
}

.urgency-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.radio-option:hover {
  border-color: #52c41a;
}

.radio-label {
  font-weight: 500;
  color: #333;
}

.radio-desc {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

.submit-btn {
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.submit-btn:hover {
  background: #73d13d;
}

.submit-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

/* 咨询记录 */
.consultation-history {
  padding: 16px;
  background: white;
  margin-top: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.more-link {
  font-size: 14px;
  color: #52c41a;
  cursor: pointer;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background: #f0f0f0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.consultation-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.expert-name {
  font-size: 12px;
  color: #666;
}

.consultation-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.consultation-status.completed {
  background: #f6ffed;
  color: #52c41a;
}

.consultation-status.in-progress {
  background: #e6f7ff;
  color: #1890ff;
}

.consultation-status.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.consultation-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

.consultation-rating {
  display: flex;
  align-items: center;
  gap: 2px;
  color: #faad14;
}
</style>
