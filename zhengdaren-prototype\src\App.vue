<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

#app {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 通用样式 */
.container {
  max-width: 375px;
  margin: 0 auto;
  background-color: #fff;
  min-height: 100vh;
  position: relative;
}

.page-content {
  padding-bottom: 60px; /* 为底部导航留出空间 */
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #1890ff;
  color: white;
}

.btn-primary:hover {
  background-color: #40a9ff;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #666;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 12px;
  overflow: hidden;
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.card-body {
  padding: 16px;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 2px 8px;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 12px;
  font-size: 12px;
  margin-right: 8px;
  margin-bottom: 4px;
}

.tag-primary {
  background-color: #e6f7ff;
  color: #1890ff;
}

.tag-success {
  background-color: #f6ffed;
  color: #52c41a;
}

.tag-warning {
  background-color: #fff7e6;
  color: #fa8c16;
}
</style>
