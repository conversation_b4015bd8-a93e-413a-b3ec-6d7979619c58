<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部搜索栏 -->
      <div class="header">
        <div class="search-bar" @click="goToSearch">
          <i class="fas fa-search"></i>
          <span class="search-placeholder">搜索政策、关键词</span>
        </div>
        <div class="user-avatar" @click="goToProfile">
          <i class="fas fa-user-circle"></i>
        </div>
      </div>

      <!-- 轮播图 -->
      <div class="banner-section">
        <div class="banner-item" v-for="banner in banners" :key="banner.id">
          <div class="banner-content">
            <h3>{{ banner.title }}</h3>
            <p>{{ banner.subtitle }}</p>
          </div>
          <div class="banner-icon">
            <i :class="banner.icon"></i>
          </div>
        </div>
      </div>

      <!-- 快速入口 -->
      <div class="quick-access">
        <h3 class="section-title">快速入口</h3>
        <div class="access-grid">
          <div 
            class="access-item" 
            v-for="item in quickAccess" 
            :key="item.id"
            @click="handleQuickAccess(item)"
          >
            <div class="access-icon">
              <i :class="item.icon"></i>
            </div>
            <span class="access-text">{{ item.name }}</span>
          </div>
        </div>
      </div>

      <!-- 个性化推荐 -->
      <div class="recommendation-section">
        <div class="section-header">
          <h3 class="section-title">为您推荐</h3>
          <span class="more-link" @click="goToDiscover">更多 <i class="fas fa-chevron-right"></i></span>
        </div>
        <div class="policy-list">
          <div 
            class="policy-item" 
            v-for="policy in recommendedPolicies" 
            :key="policy.id"
            @click="goToPolicyDetail(policy)"
          >
            <div class="policy-header">
              <h4 class="policy-title">{{ policy.title }}</h4>
              <span class="policy-date">{{ policy.publishDate }}</span>
            </div>
            <p class="policy-summary">{{ policy.summary }}</p>
            <div class="policy-tags">
              <span 
                class="tag" 
                :class="getTagClass(tag)" 
                v-for="tag in policy.tags" 
                :key="tag"
              >
                {{ tag }}
              </span>
            </div>
            <div class="policy-meta">
              <span class="meta-item">
                <i class="fas fa-eye"></i> {{ policy.views }}
              </span>
              <span class="meta-item">
                <i class="fas fa-heart"></i> {{ policy.likes }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 热门政策 -->
      <div class="hot-policies">
        <h3 class="section-title">热门政策</h3>
        <div class="hot-list">
          <div 
            class="hot-item" 
            v-for="(policy, index) in hotPolicies" 
            :key="policy.id"
            @click="goToPolicyDetail(policy)"
          >
            <div class="hot-rank" :class="{ top: index < 3 }">{{ index + 1 }}</div>
            <div class="hot-content">
              <h4 class="hot-title">{{ policy.title }}</h4>
              <div class="hot-meta">
                <span class="hot-views">{{ policy.views }}次浏览</span>
                <span class="hot-category">{{ policy.category }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'Home',
  components: {
    BottomNavigation
  },
  data() {
    return {
      banners: [
        {
          id: 1,
          title: '智能政策解读',
          subtitle: 'AI为您解读复杂政策条文',
          icon: 'fas fa-robot'
        }
      ],
      quickAccess: [
        { id: 1, name: '行业政策', icon: 'fas fa-industry', type: 'function', value: 'industry' },
        { id: 2, name: '会员服务', icon: 'fas fa-crown', type: 'function', value: 'membership' },
        { id: 3, name: '税收优惠', icon: 'fas fa-percentage', type: 'category', value: 'tax' },
        { id: 4, name: '创业扶持', icon: 'fas fa-rocket', type: 'category', value: 'startup' },
        { id: 5, name: '智能问答', icon: 'fas fa-comments', type: 'function', value: 'qa' },
        { id: 6, name: '申请指导', icon: 'fas fa-clipboard-list', type: 'function', value: 'application-guide' },
        { id: 7, name: '政策工具', icon: 'fas fa-tools', type: 'function', value: 'tools' },
        { id: 8, name: '专家咨询', icon: 'fas fa-user-tie', type: 'function', value: 'expert' }
      ],
      recommendedPolicies: [
        {
          id: 1,
          title: '关于支持中小企业数字化转型的若干措施',
          summary: '为加快推进中小企业数字化转型，提升企业竞争力，制定本措施...',
          publishDate: '2024-01-15',
          tags: ['企业扶持', '数字化', '中小企业'],
          views: 1250,
          likes: 89,
          category: '企业政策'
        },
        {
          id: 2,
          title: '个人所得税专项附加扣除暂行办法',
          summary: '为贯彻落实修改后的个人所得税法，现就个人所得税专项附加扣除有关事项...',
          publishDate: '2024-01-10',
          tags: ['个人税收', '专项扣除', '减税'],
          views: 2100,
          likes: 156,
          category: '税收政策'
        }
      ],
      hotPolicies: [
        { id: 1, title: '2024年创业担保贷款政策', views: 5200, category: '创业扶持' },
        { id: 2, title: '高新技术企业认定管理办法', views: 4800, category: '企业认定' },
        { id: 3, title: '职业技能提升行动方案', views: 4200, category: '技能培训' },
        { id: 4, title: '小微企业普惠性税收减免政策', views: 3900, category: '税收优惠' },
        { id: 5, title: '大学生就业创业扶持政策', views: 3600, category: '就业创业' }
      ]
    }
  },
  methods: {
    goToSearch() {
      this.$router.push('/search')
    },
    goToProfile() {
      this.$router.push('/profile')
    },
    goToDiscover() {
      this.$router.push('/discover')
    },
    goToPolicyDetail(policy) {
      this.$router.push(`/policy/${policy.id}`)
    },
    handleQuickAccess(item) {
      if (item.type === 'category') {
        this.$router.push(`/discover?category=${item.value}`)
      } else if (item.type === 'function') {
        if (item.value === 'qa') {
          this.$router.push('/qa')
        } else if (item.value === 'industry') {
          this.$router.push('/industry')
        } else if (item.value === 'membership') {
          this.$router.push('/membership')
        } else if (item.value === 'tools') {
          this.$router.push('/tools/1')
        } else if (item.value === 'expert') {
          this.$router.push('/membership') // 专家咨询需要会员
        } else if (item.value === 'application-guide') {
          this.$router.push('/application-guide')
        } else {
          // 其他功能页面
          this.$router.push(`/${item.value}`)
        }
      }
    },
    getTagClass(tag) {
      const tagMap = {
        '企业扶持': 'tag-primary',
        '数字化': 'tag-success',
        '个人税收': 'tag-warning',
        '减税': 'tag-success'
      }
      return tagMap[tag] || 'tag'
    }
  }
}
</script>

<style scoped>
/* 顶部搜索栏 */
.header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 8px 16px;
  margin-right: 12px;
  cursor: pointer;
}

.search-bar i {
  color: #999;
  margin-right: 8px;
}

.search-placeholder {
  color: #999;
  font-size: 14px;
}

.user-avatar {
  font-size: 24px;
  color: #1890ff;
  cursor: pointer;
}

/* 轮播图 */
.banner-section {
  margin: 12px 16px;
}

.banner-item {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 12px;
  padding: 20px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.banner-content h3 {
  font-size: 18px;
  margin-bottom: 4px;
}

.banner-content p {
  font-size: 14px;
  opacity: 0.9;
}

.banner-icon {
  font-size: 32px;
  opacity: 0.8;
}

/* 快速入口 */
.quick-access {
  margin: 20px 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.access-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.access-item:hover {
  transform: translateY(-2px);
}

.access-icon {
  width: 40px;
  height: 40px;
  background: #f0f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.access-icon i {
  font-size: 18px;
  color: #1890ff;
}

.access-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 推荐政策 */
.recommendation-section {
  margin: 20px 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.more-link {
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
}

.policy-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.policy-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.policy-item:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.policy-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
}

.policy-date {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.policy-summary {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.policy-tags {
  margin-bottom: 12px;
}

.policy-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 热门政策 */
.hot-policies {
  margin: 20px 16px;
}

.hot-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.hot-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.hot-item:last-child {
  border-bottom: none;
}

.hot-item:hover {
  background-color: #f9f9f9;
}

.hot-rank {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  margin-right: 12px;
}

.hot-rank.top {
  background: #ff4d4f;
  color: white;
}

.hot-content {
  flex: 1;
}

.hot-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.hot-meta {
  display: flex;
  gap: 12px;
}

.hot-views,
.hot-category {
  font-size: 12px;
  color: #999;
}
</style>
