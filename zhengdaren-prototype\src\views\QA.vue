<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部标题 -->
      <div class="header">
        <h2>智能问答</h2>
        <div class="header-actions">
          <i class="fas fa-history" @click="showHistory = true"></i>
        </div>
      </div>

      <!-- 快速问题 -->
      <div class="quick-questions" v-if="messages.length === 0">
        <h3>常见问题</h3>
        <div class="question-list">
          <div 
            class="question-item" 
            v-for="question in quickQuestions" 
            :key="question.id"
            @click="askQuestion(question.text)"
          >
            <i :class="question.icon"></i>
            <span>{{ question.text }}</span>
          </div>
        </div>
      </div>

      <!-- 聊天消息 -->
      <div class="chat-messages" ref="messagesContainer">
        <div 
          class="message" 
          :class="{ 'user-message': message.type === 'user', 'ai-message': message.type === 'ai' }"
          v-for="message in messages" 
          :key="message.id"
        >
          <div class="message-avatar">
            <i v-if="message.type === 'user'" class="fas fa-user"></i>
            <i v-else class="fas fa-robot"></i>
          </div>
          <div class="message-content">
            <div class="message-text" v-html="message.content"></div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            
            <!-- AI消息的操作按钮 -->
            <div class="message-actions" v-if="message.type === 'ai'">
              <button class="action-btn" @click="likeMessage(message)">
                <i :class="message.liked ? 'fas fa-thumbs-up' : 'far fa-thumbs-up'"></i>
              </button>
              <button class="action-btn" @click="dislikeMessage(message)">
                <i :class="message.disliked ? 'fas fa-thumbs-down' : 'far fa-thumbs-down'"></i>
              </button>
              <button class="action-btn" @click="copyMessage(message)">
                <i class="fas fa-copy"></i>
              </button>
            </div>

            <!-- 相关政策推荐 -->
            <div class="related-policies" v-if="message.relatedPolicies && message.relatedPolicies.length > 0">
              <h4>相关政策</h4>
              <div 
                class="policy-item" 
                v-for="policy in message.relatedPolicies" 
                :key="policy.id"
                @click="goToPolicyDetail(policy)"
              >
                <div class="policy-title">{{ policy.title }}</div>
                <div class="policy-summary">{{ policy.summary }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 正在输入指示器 -->
        <div class="typing-indicator" v-if="isTyping">
          <div class="message ai-message">
            <div class="message-avatar">
              <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
              <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-wrapper">
        <textarea
          v-model="inputText"
          placeholder="请输入您的问题..."
          @keyup.enter="sendMessage"
          @input="adjustTextareaHeight"
          ref="textarea"
          rows="1"
        ></textarea>
        <button
          class="send-btn"
          :disabled="!inputText.trim() || isTyping"
          @click="sendMessage"
        >
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>

      <!-- 功能按钮 -->
      <div class="input-actions">
        <button class="action-btn" @click="showVoiceInput">
          <i class="fas fa-microphone"></i>
        </button>
        <button class="action-btn" @click="clearChat">
          <i class="fas fa-trash"></i>
        </button>
      </div>
    </div>

    <!-- 历史记录弹窗 -->
    <div class="history-modal" v-if="showHistory" @click="showHistory = false">
      <div class="history-content" @click.stop>
        <div class="history-header">
          <h3>历史记录</h3>
          <i class="fas fa-times" @click="showHistory = false"></i>
        </div>
        <div class="history-list">
          <div
            class="history-item"
            v-for="item in chatHistory"
            :key="item.id"
            @click="loadHistory(item)"
          >
            <div class="history-title">{{ item.title }}</div>
            <div class="history-time">{{ formatDate(item.timestamp) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'QA',
  components: {
    BottomNavigation
  },
  data() {
    return {
      inputText: '',
      messages: [],
      isTyping: false,
      showHistory: false,
      messageIdCounter: 1,
      quickQuestions: [
        {
          id: 1,
          text: '如何申请创业担保贷款？',
          icon: 'fas fa-question-circle'
        },
        {
          id: 2,
          text: '中小企业有哪些税收优惠政策？',
          icon: 'fas fa-question-circle'
        },
        {
          id: 3,
          text: '个人所得税专项扣除包括哪些？',
          icon: 'fas fa-question-circle'
        },
        {
          id: 4,
          text: '高新技术企业认定条件是什么？',
          icon: 'fas fa-question-circle'
        }
      ],
      chatHistory: [
        {
          id: 1,
          title: '创业担保贷款申请流程',
          timestamp: new Date('2024-01-15 10:30:00')
        },
        {
          id: 2,
          title: '个人所得税专项扣除',
          timestamp: new Date('2024-01-14 15:20:00')
        }
      ]
    }
  },
  methods: {
    askQuestion(question) {
      this.inputText = question
      this.sendMessage()
    },
    async sendMessage() {
      if (!this.inputText.trim() || this.isTyping) return

      const userMessage = {
        id: this.messageIdCounter++,
        type: 'user',
        content: this.inputText,
        timestamp: new Date()
      }

      this.messages.push(userMessage)
      const question = this.inputText
      this.inputText = ''
      this.adjustTextareaHeight()

      // 显示正在输入
      this.isTyping = true
      this.scrollToBottom()

      // 模拟AI回复
      setTimeout(() => {
        const aiResponse = this.generateAIResponse(question)
        this.messages.push(aiResponse)
        this.isTyping = false
        this.scrollToBottom()
      }, 1500)
    },
    generateAIResponse(question) {
      // 模拟AI回复逻辑
      let response = ''
      let relatedPolicies = []

      if (question.includes('创业担保贷款')) {
        response = `
          <p><strong>创业担保贷款申请流程如下：</strong></p>
          <ol>
            <li>准备申请材料：身份证、营业执照、创业计划书等</li>
            <li>到当地人社部门或合作银行提交申请</li>
            <li>等待审核，一般需要15-20个工作日</li>
            <li>审核通过后签订贷款合同</li>
            <li>银行放款</li>
          </ol>
          <p><strong>贷款额度：</strong>个人最高20万元，小微企业最高300万元</p>
          <p><strong>贷款期限：</strong>最长3年，可展期1次</p>
        `
        relatedPolicies = [
          {
            id: 3,
            title: '2024年创业担保贷款政策',
            summary: '提高贷款额度，降低申请门槛，支持创业带动就业'
          }
        ]
      } else if (question.includes('税收优惠')) {
        response = `
          <p><strong>中小企业主要税收优惠政策包括：</strong></p>
          <ul>
            <li><strong>小微企业所得税优惠：</strong>年应纳税所得额不超过100万元的部分，减按12.5%计入应纳税所得额</li>
            <li><strong>增值税小规模纳税人优惠：</strong>月销售额15万元以下免征增值税</li>
            <li><strong>研发费用加计扣除：</strong>研发费用按175%在税前扣除</li>
            <li><strong>高新技术企业优惠：</strong>减按15%的税率征收企业所得税</li>
          </ul>
        `
        relatedPolicies = [
          {
            id: 4,
            title: '小微企业普惠性税收减免政策',
            summary: '进一步支持小微企业发展，减轻税收负担'
          }
        ]
      } else {
        response = `
          <p>感谢您的提问！我正在为您查找相关政策信息...</p>
          <p>如果您需要更详细的信息，建议您：</p>
          <ul>
            <li>查看相关政策详情页面</li>
            <li>联系当地政务服务中心</li>
            <li>咨询专业人士</li>
          </ul>
        `
      }

      return {
        id: this.messageIdCounter++,
        type: 'ai',
        content: response,
        timestamp: new Date(),
        relatedPolicies: relatedPolicies,
        liked: false,
        disliked: false
      }
    },
    adjustTextareaHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.textarea
        textarea.style.height = 'auto'
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
      })
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer
        container.scrollTop = container.scrollHeight
      })
    },
    likeMessage(message) {
      message.liked = !message.liked
      if (message.liked) {
        message.disliked = false
      }
    },
    dislikeMessage(message) {
      message.disliked = !message.disliked
      if (message.disliked) {
        message.liked = false
      }
    },
    copyMessage(message) {
      // 复制消息内容
      const textContent = message.content.replace(/<[^>]*>/g, '')
      navigator.clipboard.writeText(textContent).then(() => {
        alert('已复制到剪贴板')
      })
    },
    clearChat() {
      if (confirm('确定要清空聊天记录吗？')) {
        this.messages = []
      }
    },
    showVoiceInput() {
      alert('语音输入功能开发中...')
    },
    loadHistory(item) {
      this.showHistory = false
      // 加载历史聊天记录
      alert(`加载历史记录: ${item.title}`)
    },
    goToPolicyDetail(policy) {
      this.$router.push(`/policy/${policy.id}`)
    },
    formatTime(timestamp) {
      return timestamp.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },
    formatDate(timestamp) {
      return timestamp.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
/* 头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.header h2 {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* 快速问题 */
.quick-questions {
  padding: 20px 16px;
}

.quick-questions h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
}

.question-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.question-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.question-item i {
  color: #1890ff;
  font-size: 16px;
}

.question-item span {
  font-size: 14px;
  color: #333;
}

/* 聊天消息 */
.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.message {
  display: flex;
  margin-bottom: 16px;
}

.user-message {
  justify-content: flex-end;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background: #1890ff;
  color: white;
  margin-left: 8px;
  order: 2;
}

.ai-message .message-avatar {
  background: #f0f0f0;
  color: #666;
  margin-right: 8px;
}

.message-content {
  max-width: 70%;
  background: white;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.user-message .message-content {
  background: #1890ff;
  color: white;
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 4px;
}

.message-text p {
  margin: 8px 0;
}

.message-text ul, .message-text ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message-text li {
  margin: 4px 0;
}

.message-time {
  font-size: 12px;
  color: #999;
  text-align: right;
}

.user-message .message-time {
  color: rgba(255,255,255,0.8);
}

.message-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.message-actions .action-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.message-actions .action-btn:hover {
  background: #f0f0f0;
  color: #666;
}

/* 相关政策 */
.related-policies {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.related-policies h4 {
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.related-policies .policy-item {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.related-policies .policy-item:hover {
  background: #f0f0f0;
}

.related-policies .policy-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.related-policies .policy-summary {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 正在输入指示器 */
.typing-indicator {
  margin-bottom: 16px;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

/* 输入区域 */
.input-area {
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 375px;
  background: white;
  border-top: 1px solid #f0f0f0;
  padding: 12px 16px;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  margin-bottom: 8px;
}

.input-wrapper textarea {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  resize: none;
  outline: none;
  min-height: 36px;
  max-height: 120px;
}

.send-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.send-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.input-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.input-actions .action-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.input-actions .action-btn:hover {
  background: #f0f0f0;
  color: #666;
}

/* 历史记录弹窗 */
.history-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.history-content {
  background: white;
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 320px;
  max-height: 60vh;
  overflow-y: auto;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.history-header h3 {
  font-size: 16px;
  font-weight: 500;
}

.history-header i {
  font-size: 18px;
  color: #999;
  cursor: pointer;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background: #f9f9f9;
}

.history-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.history-time {
  font-size: 12px;
  color: #999;
}
</style>
