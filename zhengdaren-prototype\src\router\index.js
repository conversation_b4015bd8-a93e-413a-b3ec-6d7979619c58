import Vue from 'vue'
import Router from 'vue-router'
import Home from '@/views/Home'
import Discover from '@/views/Discover'
import QA from '@/views/QA'
import Profile from '@/views/Profile'
import PolicyDetail from '@/views/PolicyDetail'
import Industry from '@/views/Industry'
import Membership from '@/views/Membership'
import PolicyTool from '@/views/PolicyTool'
import AIAssistant from '@/views/AIAssistant'
import ExpertConsultation from '@/views/ExpertConsultation'
import PolicySubscription from '@/views/PolicySubscription'
import ApplicationGuide from '@/views/ApplicationGuide'
import CompanyProfile from '@/views/CompanyProfile'

Vue.use(Router)

export default new Router({
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home
    },
    {
      path: '/discover',
      name: 'Discover',
      component: Discover
    },
    {
      path: '/qa',
      name: 'QA',
      component: QA
    },
    {
      path: '/profile',
      name: 'Profile',
      component: Profile
    },
    {
      path: '/policy/:id',
      name: 'PolicyDetail',
      component: PolicyDetail
    },
    {
      path: '/industry',
      name: 'Industry',
      component: Industry
    },
    {
      path: '/membership',
      name: 'Membership',
      component: Membership
    },
    {
      path: '/tools/:id',
      name: 'PolicyTool',
      component: PolicyTool
    },
    {
      path: '/ai-assistant',
      name: 'AIAssistant',
      component: AIAssistant
    },
    {
      path: '/expert-consultation',
      name: 'ExpertConsultation',
      component: ExpertConsultation
    },
    {
      path: '/subscription',
      name: 'PolicySubscription',
      component: PolicySubscription
    },
    {
      path: '/application-guide',
      name: 'ApplicationGuide',
      component: ApplicationGuide
    },
    {
      path: '/company-profile',
      name: 'CompanyProfile',
      component: CompanyProfile
    }
  ]
})
