<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部导航 -->
      <div class="header">
        <i class="fas fa-arrow-left back-btn" @click="goBack"></i>
        <h2>申请指导</h2>
        <div class="header-actions">
          <i class="fas fa-bookmark" @click="toggleBookmark"></i>
        </div>
      </div>

      <!-- 申请向导 -->
      <div class="application-wizard">
        <div class="wizard-header">
          <h3>智能申请向导</h3>
          <p>AI为您提供个性化的政策申请指导</p>
        </div>
        
        <div class="wizard-steps">
          <div 
            class="step-item" 
            :class="{ active: currentStep === index + 1, completed: currentStep > index + 1 }"
            v-for="(step, index) in wizardSteps" 
            :key="step.id"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h4>{{ step.title }}</h4>
              <p>{{ step.description }}</p>
            </div>
          </div>
        </div>
        
        <div class="wizard-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
          </div>
          <span class="progress-text">{{ currentStep }}/{{ wizardSteps.length }} 步骤</span>
        </div>
      </div>

      <!-- 当前步骤内容 -->
      <div class="step-content-area">
        <!-- 步骤1：选择政策 -->
        <div v-if="currentStep === 1" class="step-panel">
          <h4>选择要申请的政策</h4>
          <div class="policy-selection">
            <div 
              class="policy-option" 
              :class="{ selected: selectedPolicy === policy.id }"
              v-for="policy in availablePolicies" 
              :key="policy.id"
              @click="selectPolicy(policy.id)"
            >
              <div class="policy-info">
                <h5>{{ policy.title }}</h5>
                <p>{{ policy.summary }}</p>
                <div class="policy-benefits">
                  <span class="benefit-tag" v-for="benefit in policy.benefits" :key="benefit">
                    {{ benefit }}
                  </span>
                </div>
              </div>
              <div class="policy-match">
                <div class="match-score">{{ policy.matchScore }}%</div>
                <div class="match-label">匹配度</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2：资格检查 -->
        <div v-if="currentStep === 2" class="step-panel">
          <h4>资格条件检查</h4>
          <div class="eligibility-check">
            <div class="check-item" v-for="condition in eligibilityConditions" :key="condition.id">
              <div class="condition-info">
                <h5>{{ condition.title }}</h5>
                <p>{{ condition.description }}</p>
              </div>
              <div class="condition-status">
                <label class="radio-group">
                  <input type="radio" :name="'condition_' + condition.id" value="yes" v-model="condition.status">
                  <span class="radio-label yes">符合</span>
                </label>
                <label class="radio-group">
                  <input type="radio" :name="'condition_' + condition.id" value="no" v-model="condition.status">
                  <span class="radio-label no">不符合</span>
                </label>
                <label class="radio-group">
                  <input type="radio" :name="'condition_' + condition.id" value="unsure" v-model="condition.status">
                  <span class="radio-label unsure">不确定</span>
                </label>
              </div>
            </div>
          </div>
          <div class="eligibility-result" v-if="eligibilityResult">
            <div class="result-card" :class="eligibilityResult.status">
              <div class="result-icon">
                <i :class="eligibilityResult.icon"></i>
              </div>
              <div class="result-content">
                <h5>{{ eligibilityResult.title }}</h5>
                <p>{{ eligibilityResult.message }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3：材料准备 -->
        <div v-if="currentStep === 3" class="step-panel">
          <h4>申请材料准备</h4>
          <div class="materials-checklist">
            <div class="material-category" v-for="category in materialCategories" :key="category.id">
              <h5>{{ category.name }}</h5>
              <div class="material-list">
                <div 
                  class="material-item" 
                  v-for="material in category.materials" 
                  :key="material.id"
                >
                  <div class="material-checkbox">
                    <input type="checkbox" :id="'material_' + material.id" v-model="material.prepared">
                    <label :for="'material_' + material.id">
                      <i class="fas fa-check"></i>
                    </label>
                  </div>
                  <div class="material-info">
                    <h6>{{ material.name }}</h6>
                    <p>{{ material.description }}</p>
                    <div class="material-meta">
                      <span class="material-type" :class="material.required ? 'required' : 'optional'">
                        {{ material.required ? '必需' : '可选' }}
                      </span>
                      <span class="material-format" v-if="material.format">{{ material.format }}</span>
                    </div>
                  </div>
                  <div class="material-actions">
                    <button class="action-btn" @click="viewTemplate(material)" v-if="material.hasTemplate">
                      <i class="fas fa-file-download"></i>
                      模板
                    </button>
                    <button class="action-btn" @click="getHelp(material)">
                      <i class="fas fa-question-circle"></i>
                      帮助
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="preparation-progress">
            <div class="progress-info">
              <span>材料准备进度：{{ preparedMaterialsCount }}/{{ totalMaterialsCount }}</span>
              <span class="progress-percentage">{{ materialProgressPercentage }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: materialProgressPercentage + '%' }"></div>
            </div>
          </div>
        </div>

        <!-- 步骤4：申请提交 -->
        <div v-if="currentStep === 4" class="step-panel">
          <h4>提交申请</h4>
          <div class="submission-info">
            <div class="info-card">
              <h5>申请信息确认</h5>
              <div class="info-item">
                <span class="info-label">申请政策：</span>
                <span class="info-value">{{ getSelectedPolicyTitle() }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">申请方式：</span>
                <span class="info-value">{{ submissionMethod }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">预计审批时间：</span>
                <span class="info-value">{{ estimatedProcessingTime }}</span>
              </div>
            </div>
            
            <div class="submission-methods">
              <h5>选择提交方式</h5>
              <div class="method-options">
                <label class="method-option" v-for="method in submissionMethods" :key="method.id">
                  <input type="radio" v-model="submissionMethod" :value="method.name">
                  <div class="method-card">
                    <div class="method-icon">
                      <i :class="method.icon"></i>
                    </div>
                    <div class="method-info">
                      <h6>{{ method.name }}</h6>
                      <p>{{ method.description }}</p>
                    </div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 导航按钮 -->
        <div class="wizard-navigation">
          <button 
            class="nav-btn prev" 
            @click="previousStep" 
            :disabled="currentStep === 1"
          >
            <i class="fas fa-chevron-left"></i>
            上一步
          </button>
          <button 
            class="nav-btn next" 
            @click="nextStep" 
            :disabled="!canProceedToNext"
          >
            {{ currentStep === wizardSteps.length ? '提交申请' : '下一步' }}
            <i class="fas fa-chevron-right" v-if="currentStep < wizardSteps.length"></i>
            <i class="fas fa-paper-plane" v-else></i>
          </button>
        </div>
      </div>

      <!-- 申请记录 -->
      <div class="application-history" v-if="applicationHistory.length > 0">
        <div class="section-header">
          <h4>我的申请记录</h4>
          <span class="more-link" @click="viewAllApplications">查看全部</span>
        </div>
        <div class="history-list">
          <div 
            class="history-item" 
            v-for="application in applicationHistory" 
            :key="application.id"
            @click="viewApplicationDetail(application)"
          >
            <div class="application-info">
              <h5>{{ application.policyTitle }}</h5>
              <p>申请时间：{{ application.submitDate }}</p>
            </div>
            <div class="application-status" :class="application.status">
              {{ getStatusText(application.status) }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'ApplicationGuide',
  components: {
    BottomNavigation
  },
  data() {
    return {
      currentStep: 1,
      selectedPolicy: null,
      submissionMethod: '在线申请',
      estimatedProcessingTime: '15-20个工作日',
      wizardSteps: [
        {
          id: 1,
          title: '选择政策',
          description: '选择您要申请的政策项目'
        },
        {
          id: 2,
          title: '资格检查',
          description: '检查是否符合申请条件'
        },
        {
          id: 3,
          title: '材料准备',
          description: '准备所需的申请材料'
        },
        {
          id: 4,
          title: '提交申请',
          description: '确认信息并提交申请'
        }
      ],
      availablePolicies: [
        {
          id: 1,
          title: '制造业数字化转型专项资金',
          summary: '支持制造业企业开展数字化、网络化、智能化改造',
          benefits: ['最高500万补贴', '税收减免', '优先审批'],
          matchScore: 92
        },
        {
          id: 2,
          title: '中小企业创新发展专项资金',
          summary: '支持中小企业技术创新和产品研发',
          benefits: ['研发补贴', '创新奖励', '知识产权支持'],
          matchScore: 85
        },
        {
          id: 3,
          title: '高新技术企业认定',
          summary: '符合条件的企业可申请高新技术企业认定',
          benefits: ['15%所得税率', '研发费用加计扣除', '政策优先'],
          matchScore: 78
        }
      ],
      eligibilityConditions: [
        {
          id: 1,
          title: '企业注册时间',
          description: '企业注册成立满1年以上',
          status: null
        },
        {
          id: 2,
          title: '年营业收入',
          description: '上年度营业收入不低于500万元',
          status: null
        },
        {
          id: 3,
          title: '研发投入',
          description: '近三年研发费用占营业收入比例不低于3%',
          status: null
        },
        {
          id: 4,
          title: '知识产权',
          description: '拥有自主知识产权或专利',
          status: null
        }
      ],
      materialCategories: [
        {
          id: 1,
          name: '企业基础材料',
          materials: [
            {
              id: 1,
              name: '营业执照',
              description: '企业营业执照副本复印件',
              required: true,
              format: 'PDF格式',
              hasTemplate: false,
              prepared: false
            },
            {
              id: 2,
              name: '组织机构代码证',
              description: '组织机构代码证复印件',
              required: true,
              format: 'PDF格式',
              hasTemplate: false,
              prepared: false
            }
          ]
        },
        {
          id: 2,
          name: '财务材料',
          materials: [
            {
              id: 3,
              name: '审计报告',
              description: '近两年度审计报告',
              required: true,
              format: 'PDF格式',
              hasTemplate: false,
              prepared: false
            },
            {
              id: 4,
              name: '财务报表',
              description: '近三年财务报表',
              required: true,
              format: 'Excel格式',
              hasTemplate: true,
              prepared: false
            }
          ]
        },
        {
          id: 3,
          name: '项目材料',
          materials: [
            {
              id: 5,
              name: '项目申请书',
              description: '详细的项目申请书',
              required: true,
              format: 'Word格式',
              hasTemplate: true,
              prepared: false
            },
            {
              id: 6,
              name: '技术方案',
              description: '项目技术实施方案',
              required: true,
              format: 'PDF格式',
              hasTemplate: true,
              prepared: false
            }
          ]
        }
      ],
      submissionMethods: [
        {
          id: 1,
          name: '在线申请',
          description: '通过政务服务平台在线提交',
          icon: 'fas fa-laptop'
        },
        {
          id: 2,
          name: '现场申请',
          description: '到政务服务中心现场提交',
          icon: 'fas fa-building'
        },
        {
          id: 3,
          name: '邮寄申请',
          description: '通过邮寄方式提交材料',
          icon: 'fas fa-envelope'
        }
      ],
      applicationHistory: [
        {
          id: 1,
          policyTitle: '高新技术企业认定',
          submitDate: '2024-01-15',
          status: 'approved'
        },
        {
          id: 2,
          policyTitle: '中小企业创新发展专项资金',
          submitDate: '2024-01-10',
          status: 'pending'
        }
      ]
    }
  },
  computed: {
    progressPercentage() {
      return (this.currentStep / this.wizardSteps.length) * 100
    },
    eligibilityResult() {
      const conditions = this.eligibilityConditions
      if (conditions.every(c => c.status === null)) return null
      
      const yesCount = conditions.filter(c => c.status === 'yes').length
      const noCount = conditions.filter(c => c.status === 'no').length
      const unsureCount = conditions.filter(c => c.status === 'unsure').length
      
      if (noCount > 0) {
        return {
          status: 'failed',
          icon: 'fas fa-times-circle',
          title: '不符合申请条件',
          message: '您有部分条件不符合要求，建议咨询专家或选择其他政策'
        }
      } else if (unsureCount > 0) {
        return {
          status: 'warning',
          icon: 'fas fa-exclamation-triangle',
          title: '部分条件需要确认',
          message: '建议咨询专家确认相关条件，以提高申请成功率'
        }
      } else {
        return {
          status: 'success',
          icon: 'fas fa-check-circle',
          title: '符合申请条件',
          message: '恭喜！您符合所有申请条件，可以继续准备材料'
        }
      }
    },
    preparedMaterialsCount() {
      return this.materialCategories.reduce((count, category) => {
        return count + category.materials.filter(m => m.prepared).length
      }, 0)
    },
    totalMaterialsCount() {
      return this.materialCategories.reduce((count, category) => {
        return count + category.materials.length
      }, 0)
    },
    materialProgressPercentage() {
      return this.totalMaterialsCount > 0 ? 
        Math.round((this.preparedMaterialsCount / this.totalMaterialsCount) * 100) : 0
    },
    canProceedToNext() {
      switch (this.currentStep) {
        case 1:
          return this.selectedPolicy !== null
        case 2:
          return this.eligibilityConditions.every(c => c.status !== null) && 
                 this.eligibilityResult && 
                 this.eligibilityResult.status !== 'failed'
        case 3:
          const requiredMaterials = this.materialCategories.reduce((materials, category) => {
            return materials.concat(category.materials.filter(m => m.required))
          }, [])
          return requiredMaterials.every(m => m.prepared)
        case 4:
          return this.submissionMethod !== null
        default:
          return false
      }
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    toggleBookmark() {
      // 收藏功能
      alert('已收藏申请指导')
    },
    selectPolicy(policyId) {
      this.selectedPolicy = policyId
    },
    previousStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },
    nextStep() {
      if (this.currentStep < this.wizardSteps.length) {
        this.currentStep++
      } else {
        this.submitApplication()
      }
    },
    submitApplication() {
      // 提交申请
      alert('申请已提交，请等待审核结果')
      this.$router.push('/applications')
    },
    getSelectedPolicyTitle() {
      const policy = this.availablePolicies.find(p => p.id === this.selectedPolicy)
      return policy ? policy.title : ''
    },
    viewTemplate(material) {
      // 查看模板
      alert(`下载${material.name}模板`)
    },
    getHelp(material) {
      // 获取帮助
      alert(`获取${material.name}填写帮助`)
    },
    viewAllApplications() {
      this.$router.push('/applications')
    },
    viewApplicationDetail(application) {
      this.$router.push(`/application/${application.id}`)
    },
    getStatusText(status) {
      const statusMap = {
        'pending': '审核中',
        'approved': '已通过',
        'rejected': '已拒绝',
        'draft': '草稿'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* 申请向导 */
.application-wizard {
  padding: 16px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
}

.wizard-header {
  text-align: center;
  margin-bottom: 20px;
}

.wizard-header h3 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 4px;
}

.wizard-header p {
  font-size: 14px;
  opacity: 0.9;
}

.wizard-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  opacity: 0.6;
  transition: opacity 0.3s;
}

.step-item.active,
.step-item.completed {
  opacity: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255,255,255,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  margin-bottom: 8px;
}

.step-item.active .step-number {
  background: white;
  color: #1890ff;
}

.step-item.completed .step-number {
  background: #52c41a;
  color: white;
}

.step-content {
  text-align: center;
}

.step-content h4 {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.step-content p {
  font-size: 10px;
  opacity: 0.8;
}

.wizard-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255,255,255,0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: white;
  transition: width 0.3s;
}

.progress-text {
  font-size: 12px;
  white-space: nowrap;
}

/* 步骤内容 */
.step-content-area {
  padding: 16px;
}

.step-panel h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

/* 政策选择 */
.policy-selection {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.policy-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.policy-option:hover {
  border-color: #1890ff;
}

.policy-option.selected {
  border-color: #1890ff;
  background: #f0f8ff;
}

.policy-info {
  flex: 1;
}

.policy-info h5 {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.policy-info p {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}

.policy-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.benefit-tag {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.policy-match {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.match-score {
  font-size: 20px;
  font-weight: 600;
  color: #52c41a;
}

.match-label {
  font-size: 11px;
  color: #999;
}

/* 资格检查 */
.eligibility-check {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.check-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #f0f0f0;
}

.condition-info {
  margin-bottom: 12px;
}

.condition-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.condition-info p {
  font-size: 13px;
  color: #666;
}

.condition-status {
  display: flex;
  gap: 16px;
}

.radio-group {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.radio-label {
  font-size: 13px;
}

.radio-label.yes {
  color: #52c41a;
}

.radio-label.no {
  color: #ff4d4f;
}

.radio-label.unsure {
  color: #fa8c16;
}

.eligibility-result {
  margin-top: 16px;
}

.result-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
}

.result-card.success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

.result-card.warning {
  background: #fff7e6;
  border: 1px solid #ffd591;
}

.result-card.failed {
  background: #fff2f0;
  border: 1px solid #ffccc7;
}

.result-icon {
  font-size: 24px;
}

.result-card.success .result-icon {
  color: #52c41a;
}

.result-card.warning .result-icon {
  color: #fa8c16;
}

.result-card.failed .result-icon {
  color: #ff4d4f;
}

.result-content h5 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.result-content p {
  font-size: 13px;
  color: #666;
}

/* 材料准备 */
.materials-checklist {
  margin-bottom: 20px;
}

.material-category {
  margin-bottom: 20px;
}

.material-category h5 {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.material-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.material-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.material-checkbox {
  position: relative;
}

.material-checkbox input[type="checkbox"] {
  opacity: 0;
  position: absolute;
}

.material-checkbox label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 2px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.material-checkbox input[type="checkbox"]:checked + label {
  background: #52c41a;
  border-color: #52c41a;
}

.material-checkbox label i {
  color: white;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
}

.material-checkbox input[type="checkbox"]:checked + label i {
  opacity: 1;
}

.material-info {
  flex: 1;
}

.material-info h6 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.material-info p {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.material-meta {
  display: flex;
  gap: 8px;
}

.material-type {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
}

.material-type.required {
  background: #fff2f0;
  color: #ff4d4f;
}

.material-type.optional {
  background: #f0f0f0;
  color: #999;
}

.material-format {
  font-size: 10px;
  color: #999;
}

.material-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.preparation-progress {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #f0f0f0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.progress-percentage {
  font-weight: 500;
  color: #1890ff;
}

/* 提交申请 */
.submission-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #f0f0f0;
}

.info-card h5 {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-label {
  color: #666;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.submission-methods h5 {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.method-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.method-option {
  cursor: pointer;
}

.method-option input[type="radio"] {
  display: none;
}

.method-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.2s;
}

.method-option input[type="radio"]:checked + .method-card {
  border-color: #1890ff;
  background: #f0f8ff;
}

.method-icon {
  width: 40px;
  height: 40px;
  background: #f0f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.method-icon i {
  font-size: 18px;
  color: #1890ff;
}

.method-info h6 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.method-info p {
  font-size: 12px;
  color: #666;
}

/* 导航按钮 */
.wizard-navigation {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.nav-btn.prev {
  background: #f0f0f0;
  color: #666;
}

.nav-btn.prev:hover:not(:disabled) {
  background: #e0e0e0;
}

.nav-btn.next {
  background: #1890ff;
  color: white;
}

.nav-btn.next:hover:not(:disabled) {
  background: #40a9ff;
}

.nav-btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

/* 申请记录 */
.application-history {
  padding: 16px;
  background: white;
  margin-top: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.more-link {
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background: #f0f0f0;
}

.application-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.application-info p {
  font-size: 12px;
  color: #666;
}

.application-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.application-status.pending {
  background: #e6f7ff;
  color: #1890ff;
}

.application-status.approved {
  background: #f6ffed;
  color: #52c41a;
}

.application-status.rejected {
  background: #fff2f0;
  color: #ff4d4f;
}
</style>
