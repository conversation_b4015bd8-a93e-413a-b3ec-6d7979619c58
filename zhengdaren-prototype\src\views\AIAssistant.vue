<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部导航 -->
      <div class="header">
        <i class="fas fa-arrow-left back-btn" @click="goBack"></i>
        <h2>AI政策管家</h2>
        <div class="header-actions">
          <i class="fas fa-cog" @click="goToSettings"></i>
        </div>
      </div>

      <!-- AI助手介绍 -->
      <div class="assistant-intro">
        <div class="intro-card">
          <div class="ai-avatar">
            <i class="fas fa-robot"></i>
            <div class="status-indicator online"></div>
          </div>
          <div class="ai-info">
            <h3>小政 - 您的专属政策管家</h3>
            <p>24小时为您监控政策动态，提供个性化政策服务</p>
          </div>
          <div class="ai-level">
            <span class="level-badge">{{ userLevel }}</span>
          </div>
        </div>
      </div>

      <!-- 今日政策摘要 -->
      <div class="daily-summary">
        <div class="summary-header">
          <h4>今日政策摘要</h4>
          <span class="update-time">{{ updateTime }}</span>
        </div>
        <div class="summary-content">
          <div class="summary-stats">
            <div class="stat-item">
              <span class="stat-number">{{ todaySummary.newPolicies }}</span>
              <span class="stat-label">新增政策</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ todaySummary.relevantPolicies }}</span>
              <span class="stat-label">相关政策</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ todaySummary.urgentDeadlines }}</span>
              <span class="stat-label">紧急截止</span>
            </div>
          </div>
          <div class="summary-highlights">
            <div 
              class="highlight-item" 
              v-for="highlight in todaySummary.highlights" 
              :key="highlight.id"
              @click="viewHighlight(highlight)"
            >
              <div class="highlight-icon" :class="highlight.type">
                <i :class="highlight.icon"></i>
              </div>
              <div class="highlight-content">
                <h5>{{ highlight.title }}</h5>
                <p>{{ highlight.description }}</p>
              </div>
              <div class="highlight-time">{{ highlight.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 智能提醒 -->
      <div class="smart-reminders">
        <div class="section-header">
          <h4>智能提醒</h4>
          <span class="reminder-count">{{ reminders.length }}条</span>
        </div>
        <div class="reminder-list">
          <div 
            class="reminder-item" 
            :class="reminder.priority"
            v-for="reminder in reminders" 
            :key="reminder.id"
            @click="handleReminder(reminder)"
          >
            <div class="reminder-icon">
              <i :class="reminder.icon"></i>
            </div>
            <div class="reminder-content">
              <h5>{{ reminder.title }}</h5>
              <p>{{ reminder.description }}</p>
              <div class="reminder-meta">
                <span class="reminder-type">{{ reminder.type }}</span>
                <span class="reminder-deadline">{{ reminder.deadline }}</span>
              </div>
            </div>
            <div class="reminder-actions">
              <button class="action-btn" @click.stop="snoozeReminder(reminder)">
                <i class="fas fa-clock"></i>
              </button>
              <button class="action-btn" @click.stop="dismissReminder(reminder)">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 政策监控 -->
      <div class="policy-monitoring">
        <div class="section-header">
          <h4>政策监控</h4>
          <button class="add-monitor-btn" @click="addMonitor">
            <i class="fas fa-plus"></i>
            添加监控
          </button>
        </div>
        <div class="monitor-list">
          <div 
            class="monitor-item" 
            v-for="monitor in policyMonitors" 
            :key="monitor.id"
            @click="viewMonitorDetail(monitor)"
          >
            <div class="monitor-header">
              <div class="monitor-info">
                <h5>{{ monitor.name }}</h5>
                <div class="monitor-tags">
                  <span class="tag" v-for="tag in monitor.tags" :key="tag">{{ tag }}</span>
                </div>
              </div>
              <div class="monitor-status" :class="monitor.status">
                <i :class="getStatusIcon(monitor.status)"></i>
                <span>{{ getStatusText(monitor.status) }}</span>
              </div>
            </div>
            <div class="monitor-stats">
              <div class="stat">
                <span class="stat-label">监控天数</span>
                <span class="stat-value">{{ monitor.days }}天</span>
              </div>
              <div class="stat">
                <span class="stat-label">发现政策</span>
                <span class="stat-value">{{ monitor.foundPolicies }}条</span>
              </div>
              <div class="stat">
                <span class="stat-label">最近更新</span>
                <span class="stat-value">{{ monitor.lastUpdate }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI分析报告 -->
      <div class="ai-reports">
        <div class="section-header">
          <h4>AI分析报告</h4>
          <span class="more-link" @click="viewAllReports">查看全部</span>
        </div>
        <div class="report-list">
          <div 
            class="report-item" 
            v-for="report in aiReports" 
            :key="report.id"
            @click="viewReport(report)"
          >
            <div class="report-icon">
              <i :class="report.icon"></i>
            </div>
            <div class="report-content">
              <h5>{{ report.title }}</h5>
              <p>{{ report.summary }}</p>
              <div class="report-meta">
                <span class="report-type">{{ report.type }}</span>
                <span class="report-date">{{ report.date }}</span>
              </div>
            </div>
            <div class="report-badge" v-if="report.isNew">NEW</div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <h4>快速操作</h4>
        <div class="action-grid">
          <div 
            class="action-item" 
            v-for="action in quickActions" 
            :key="action.id"
            @click="handleQuickAction(action)"
          >
            <div class="action-icon" :style="{ backgroundColor: action.color }">
              <i :class="action.icon"></i>
            </div>
            <span class="action-name">{{ action.name }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'AIAssistant',
  components: {
    BottomNavigation
  },
  data() {
    return {
      userLevel: 'PRO',
      updateTime: '今天 09:30 更新',
      todaySummary: {
        newPolicies: 12,
        relevantPolicies: 5,
        urgentDeadlines: 2,
        highlights: [
          {
            id: 1,
            type: 'new',
            icon: 'fas fa-plus-circle',
            title: '制造业数字化转型新政策发布',
            description: '最高补贴500万元，申请截止时间3月31日',
            time: '2小时前'
          },
          {
            id: 2,
            type: 'urgent',
            icon: 'fas fa-exclamation-triangle',
            title: '高新技术企业认定即将截止',
            description: '距离申请截止还有3天，请尽快准备材料',
            time: '30分钟前'
          },
          {
            id: 3,
            type: 'match',
            icon: 'fas fa-bullseye',
            title: '发现高匹配度政策',
            description: '中小企业创新发展专项资金，匹配度92%',
            time: '1小时前'
          }
        ]
      },
      reminders: [
        {
          id: 1,
          priority: 'high',
          icon: 'fas fa-exclamation-circle',
          title: '政策申请材料准备提醒',
          description: '您关注的"数字化转型补贴"需要准备财务报表等材料',
          type: '申请提醒',
          deadline: '3天后截止'
        },
        {
          id: 2,
          priority: 'medium',
          icon: 'fas fa-bell',
          title: '政策解读直播通知',
          description: '明天下午2点将有专家解读最新税收优惠政策',
          type: '活动提醒',
          deadline: '明天 14:00'
        },
        {
          id: 3,
          priority: 'low',
          icon: 'fas fa-info-circle',
          title: '政策更新通知',
          description: '您订阅的"创业扶持"类政策有2条更新',
          type: '更新提醒',
          deadline: '今天'
        }
      ],
      policyMonitors: [
        {
          id: 1,
          name: '制造业政策监控',
          tags: ['制造业', '数字化转型', '智能制造'],
          status: 'active',
          days: 45,
          foundPolicies: 23,
          lastUpdate: '2小时前'
        },
        {
          id: 2,
          name: '税收优惠监控',
          tags: ['税收优惠', '小微企业', '研发费用'],
          status: 'active',
          days: 30,
          foundPolicies: 15,
          lastUpdate: '1天前'
        },
        {
          id: 3,
          name: '人才政策监控',
          tags: ['人才引进', '培训补贴', '技能提升'],
          status: 'paused',
          days: 60,
          foundPolicies: 8,
          lastUpdate: '3天前'
        }
      ],
      aiReports: [
        {
          id: 1,
          icon: 'fas fa-chart-line',
          title: '本月政策趋势分析',
          summary: '制造业政策支持力度加大，数字化转型成为重点方向',
          type: '趋势分析',
          date: '2024-01-25',
          isNew: true
        },
        {
          id: 2,
          icon: 'fas fa-target',
          title: '个性化政策推荐报告',
          summary: '基于您的企业画像，为您推荐5项高匹配度政策',
          type: '推荐报告',
          date: '2024-01-23',
          isNew: false
        },
        {
          id: 3,
          icon: 'fas fa-exclamation-triangle',
          title: '政策风险预警报告',
          summary: '检测到2项政策可能影响您的业务，建议关注',
          type: '风险预警',
          date: '2024-01-20',
          isNew: false
        }
      ],
      quickActions: [
        {
          id: 1,
          name: '政策搜索',
          icon: 'fas fa-search',
          color: '#1890ff'
        },
        {
          id: 2,
          name: '专家咨询',
          icon: 'fas fa-user-tie',
          color: '#52c41a'
        },
        {
          id: 3,
          name: '申请指导',
          icon: 'fas fa-clipboard-list',
          color: '#fa8c16'
        },
        {
          id: 4,
          name: '政策对比',
          icon: 'fas fa-balance-scale',
          color: '#722ed1'
        }
      ]
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToSettings() {
      this.$router.push('/ai-settings')
    },
    viewHighlight(highlight) {
      if (highlight.type === 'new') {
        this.$router.push('/discover?filter=new')
      } else if (highlight.type === 'urgent') {
        this.$router.push('/reminders?filter=urgent')
      } else if (highlight.type === 'match') {
        this.$router.push('/discover?filter=recommended')
      }
    },
    handleReminder(reminder) {
      this.$router.push(`/reminder/${reminder.id}`)
    },
    snoozeReminder(reminder) {
      // 延后提醒
      alert(`已将"${reminder.title}"延后1小时提醒`)
    },
    dismissReminder(reminder) {
      // 忽略提醒
      const index = this.reminders.findIndex(r => r.id === reminder.id)
      if (index > -1) {
        this.reminders.splice(index, 1)
      }
    },
    addMonitor() {
      this.$router.push('/add-monitor')
    },
    viewMonitorDetail(monitor) {
      this.$router.push(`/monitor/${monitor.id}`)
    },
    getStatusIcon(status) {
      const iconMap = {
        'active': 'fas fa-play-circle',
        'paused': 'fas fa-pause-circle',
        'stopped': 'fas fa-stop-circle'
      }
      return iconMap[status] || 'fas fa-question-circle'
    },
    getStatusText(status) {
      const textMap = {
        'active': '监控中',
        'paused': '已暂停',
        'stopped': '已停止'
      }
      return textMap[status] || '未知'
    },
    viewAllReports() {
      this.$router.push('/ai-reports')
    },
    viewReport(report) {
      this.$router.push(`/report/${report.id}`)
    },
    handleQuickAction(action) {
      const actionMap = {
        1: '/discover',
        2: '/expert-consultation',
        3: '/application-guide',
        4: '/policy-compare'
      }
      this.$router.push(actionMap[action.id] || '/')
    }
  }
}
</script>

<style scoped>
/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* AI助手介绍 */
.assistant-intro {
  padding: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.intro-card {
  display: flex;
  align-items: center;
  gap: 16px;
  color: white;
}

.ai-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ai-avatar i {
  font-size: 24px;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.online {
  background: #52c41a;
}

.ai-info {
  flex: 1;
}

.ai-info h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.ai-info p {
  font-size: 13px;
  opacity: 0.9;
}

.ai-level {
  flex-shrink: 0;
}

.level-badge {
  background: rgba(255,255,255,0.2);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 今日摘要 */
.daily-summary {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.summary-header h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.update-time {
  font-size: 12px;
  color: #999;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.summary-highlights {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.highlight-item:hover {
  background: #f0f0f0;
}

.highlight-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.highlight-icon.new {
  background: #e6f7ff;
  color: #1890ff;
}

.highlight-icon.urgent {
  background: #fff2e8;
  color: #fa8c16;
}

.highlight-icon.match {
  background: #f6ffed;
  color: #52c41a;
}

.highlight-content {
  flex: 1;
}

.highlight-content h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.highlight-content p {
  font-size: 12px;
  color: #666;
}

.highlight-time {
  font-size: 11px;
  color: #999;
}

/* 智能提醒 */
.smart-reminders {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.reminder-count {
  background: #ff4d4f;
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 8px;
}

.reminder-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reminder-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.reminder-item.high {
  background: #fff2f0;
  border-left: 4px solid #ff4d4f;
}

.reminder-item.medium {
  background: #fff7e6;
  border-left: 4px solid #fa8c16;
}

.reminder-item.low {
  background: #f6ffed;
  border-left: 4px solid #52c41a;
}

.reminder-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.reminder-item.high .reminder-icon {
  background: #ffebee;
  color: #ff4d4f;
}

.reminder-item.medium .reminder-icon {
  background: #fff3e0;
  color: #fa8c16;
}

.reminder-item.low .reminder-icon {
  background: #f1f8e9;
  color: #52c41a;
}

.reminder-content {
  flex: 1;
}

.reminder-content h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.reminder-content p {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.reminder-meta {
  display: flex;
  gap: 12px;
}

.reminder-type,
.reminder-deadline {
  font-size: 11px;
  color: #999;
}

.reminder-actions {
  display: flex;
  gap: 8px;
}

.reminder-actions .action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reminder-actions .action-btn:hover {
  background: #e0e0e0;
}

.reminder-actions .action-btn i {
  font-size: 10px;
  color: #666;
}

/* 政策监控 */
.policy-monitoring {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.add-monitor-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
}

.monitor-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.monitor-item {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.monitor-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.monitor-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.monitor-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.monitor-tags .tag {
  background: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.monitor-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.monitor-status.active {
  color: #52c41a;
}

.monitor-status.paused {
  color: #fa8c16;
}

.monitor-status.stopped {
  color: #ff4d4f;
}

.monitor-stats {
  display: flex;
  justify-content: space-between;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 11px;
  color: #999;
}

.stat-value {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

/* AI分析报告 */
.ai-reports {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.more-link {
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
}

.report-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.report-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.report-item:hover {
  background: #f0f0f0;
}

.report-icon {
  width: 32px;
  height: 32px;
  background: #e6f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.report-icon i {
  font-size: 14px;
  color: #1890ff;
}

.report-content {
  flex: 1;
}

.report-content h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.report-content p {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.report-meta {
  display: flex;
  gap: 12px;
}

.report-type,
.report-date {
  font-size: 11px;
  color: #999;
}

.report-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 快速操作 */
.quick-actions {
  padding: 16px;
  background: white;
}

.quick-actions h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.action-item:hover {
  transform: translateY(-2px);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon i {
  font-size: 18px;
  color: white;
}

.action-name {
  font-size: 12px;
  color: #666;
  text-align: center;
}
</style>
