<template>
  <div class="container">
    <div class="page-content">
      <!-- 用户信息 -->
      <div class="user-info">
        <div class="user-avatar">
          <img v-if="user.avatar" :src="user.avatar" alt="头像">
          <i v-else class="fas fa-user-circle"></i>
        </div>
        <div class="user-details">
          <h3 class="user-name">{{ user.name || '未登录' }}</h3>
          <p class="user-type">{{ getUserTypeText(user.type) }}</p>
          <div class="user-stats">
            <div class="stat-item">
              <span class="stat-number">{{ user.favoriteCount || 0 }}</span>
              <span class="stat-label">收藏</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ user.applicationCount || 0 }}</span>
              <span class="stat-label">申请</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ user.subscriptionCount || 0 }}</span>
              <span class="stat-label">订阅</span>
            </div>
          </div>
        </div>
        <div class="user-actions">
          <button class="edit-btn" @click="editProfile">
            <i class="fas fa-edit"></i>
          </button>
        </div>
      </div>

      <!-- 快速功能 -->
      <div class="quick-functions">
        <div 
          class="function-item" 
          v-for="func in quickFunctions" 
          :key="func.id"
          @click="handleFunction(func)"
        >
          <div class="function-icon" :style="{ backgroundColor: func.color }">
            <i :class="func.icon"></i>
          </div>
          <span class="function-name">{{ func.name }}</span>
          <i class="fas fa-chevron-right function-arrow"></i>
        </div>
      </div>

      <!-- 我的服务 -->
      <div class="my-services">
        <h3 class="section-title">我的服务</h3>
        <div class="service-grid">
          <div 
            class="service-item" 
            v-for="service in services" 
            :key="service.id"
            @click="goToService(service)"
          >
            <div class="service-icon">
              <i :class="service.icon"></i>
            </div>
            <span class="service-name">{{ service.name }}</span>
            <div class="service-badge" v-if="service.badge">
              {{ service.badge }}
            </div>
          </div>
        </div>
      </div>

      <!-- 最近浏览 -->
      <div class="recent-viewed" v-if="recentPolicies.length > 0">
        <div class="section-header">
          <h3 class="section-title">最近浏览</h3>
          <span class="more-link" @click="viewAllRecent">查看全部</span>
        </div>
        <div class="recent-list">
          <div 
            class="recent-item" 
            v-for="policy in recentPolicies.slice(0, 3)" 
            :key="policy.id"
            @click="goToPolicyDetail(policy)"
          >
            <div class="recent-content">
              <h4 class="recent-title">{{ policy.title }}</h4>
              <p class="recent-time">{{ formatTime(policy.viewTime) }}</p>
            </div>
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
      </div>

      <!-- 设置选项 -->
      <div class="settings">
        <h3 class="section-title">设置</h3>
        <div class="setting-list">
          <div 
            class="setting-item" 
            v-for="setting in settings" 
            :key="setting.id"
            @click="handleSetting(setting)"
          >
            <div class="setting-left">
              <i :class="setting.icon"></i>
              <span class="setting-name">{{ setting.name }}</span>
            </div>
            <div class="setting-right">
              <span class="setting-value" v-if="setting.value">{{ setting.value }}</span>
              <i class="fas fa-chevron-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 退出登录 -->
      <div class="logout-section" v-if="user.name">
        <button class="logout-btn" @click="logout">
          退出登录
        </button>
      </div>
      
      <!-- 登录按钮 -->
      <div class="login-section" v-else>
        <button class="login-btn" @click="login">
          立即登录
        </button>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'Profile',
  components: {
    BottomNavigation
  },
  data() {
    return {
      user: {
        name: '张三',
        type: 'individual', // individual, enterprise, organization
        avatar: '',
        favoriteCount: 12,
        applicationCount: 3,
        subscriptionCount: 8
      },
      quickFunctions: [
        {
          id: 1,
          name: '我的收藏',
          icon: 'fas fa-heart',
          color: '#ff4d4f',
          route: '/favorites'
        },
        {
          id: 2,
          name: '企业画像',
          icon: 'fas fa-chart-pie',
          color: '#1890ff',
          route: '/company-profile'
        },
        {
          id: 3,
          name: '政策订阅',
          icon: 'fas fa-bell',
          color: '#52c41a',
          route: '/subscription'
        },
        {
          id: 4,
          name: 'AI管家',
          icon: 'fas fa-robot',
          color: '#722ed1',
          route: '/ai-assistant'
        }
      ],
      services: [
        {
          id: 1,
          name: '政策对比',
          icon: 'fas fa-balance-scale',
          route: '/compare'
        },
        {
          id: 2,
          name: '申请指导',
          icon: 'fas fa-clipboard-list',
          route: '/guide'
        },
        {
          id: 3,
          name: '进度跟踪',
          icon: 'fas fa-tasks',
          route: '/tracking'
        },
        {
          id: 4,
          name: '专家咨询',
          icon: 'fas fa-user-tie',
          route: '/expert-consultation'
        },
        {
          id: 5,
          name: '申请指导',
          icon: 'fas fa-clipboard-list',
          route: '/application-guide'
        },
        {
          id: 6,
          name: '会员服务',
          icon: 'fas fa-crown',
          route: '/membership',
          badge: 'VIP'
        }
      ],
      recentPolicies: [
        {
          id: 1,
          title: '关于支持中小企业数字化转型的若干措施',
          viewTime: new Date('2024-01-15 14:30:00')
        },
        {
          id: 2,
          title: '个人所得税专项附加扣除暂行办法',
          viewTime: new Date('2024-01-15 10:20:00')
        },
        {
          id: 3,
          title: '2024年创业担保贷款政策',
          viewTime: new Date('2024-01-14 16:45:00')
        }
      ],
      settings: [
        {
          id: 1,
          name: '消息通知',
          icon: 'fas fa-bell',
          value: '已开启',
          route: '/settings/notifications'
        },
        {
          id: 2,
          name: '隐私设置',
          icon: 'fas fa-shield-alt',
          route: '/settings/privacy'
        },
        {
          id: 3,
          name: '意见反馈',
          icon: 'fas fa-comment-dots',
          route: '/feedback'
        },
        {
          id: 4,
          name: '帮助中心',
          icon: 'fas fa-question-circle',
          route: '/help'
        },
        {
          id: 5,
          name: '关于我们',
          icon: 'fas fa-info-circle',
          route: '/about'
        }
      ]
    }
  },
  methods: {
    getUserTypeText(type) {
      const typeMap = {
        'individual': '个人用户',
        'enterprise': '企业用户',
        'organization': '机构用户'
      }
      return typeMap[type] || '未知用户'
    },
    editProfile() {
      this.$router.push('/profile/edit')
    },
    handleFunction(func) {
      this.$router.push(func.route)
    },
    goToService(service) {
      this.$router.push(service.route)
    },
    goToPolicyDetail(policy) {
      this.$router.push(`/policy/${policy.id}`)
    },
    viewAllRecent() {
      this.$router.push('/recent')
    },
    handleSetting(setting) {
      this.$router.push(setting.route)
    },
    login() {
      this.$router.push('/login')
    },
    logout() {
      if (confirm('确定要退出登录吗？')) {
        this.user = {
          name: '',
          type: '',
          avatar: '',
          favoriteCount: 0,
          applicationCount: 0,
          subscriptionCount: 0
        }
        // 实际项目中这里会清除登录状态
      }
    },
    formatTime(timestamp) {
      const now = new Date()
      const diff = now - timestamp
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    }
  }
}
</script>

<style scoped>
/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  padding: 20px 16px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16px;
  background: rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-avatar i {
  font-size: 32px;
  color: rgba(255,255,255,0.8);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 4px;
}

.user-type {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 12px;
}

.user-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 16px;
  font-weight: 500;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.user-actions {
  margin-left: 16px;
}

.edit-btn {
  width: 36px;
  height: 36px;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  background: transparent;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.edit-btn:hover {
  background: rgba(255,255,255,0.1);
}

/* 快速功能 */
.quick-functions {
  margin: 16px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.function-item:last-child {
  border-bottom: none;
}

.function-item:hover {
  background-color: #f9f9f9;
}

.function-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.function-icon i {
  font-size: 16px;
  color: white;
}

.function-name {
  flex: 1;
  font-size: 15px;
  color: #333;
}

.function-arrow {
  color: #ccc;
  font-size: 12px;
}

/* 我的服务 */
.my-services {
  margin: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
  position: relative;
}

.service-item:hover {
  transform: translateY(-2px);
}

.service-icon {
  width: 40px;
  height: 40px;
  background: #f0f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.service-icon i {
  font-size: 18px;
  color: #1890ff;
}

.service-name {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.service-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 最近浏览 */
.recent-viewed {
  margin: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.more-link {
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
}

.recent-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-item:hover {
  background-color: #f9f9f9;
}

.recent-content {
  flex: 1;
}

.recent-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.recent-time {
  font-size: 12px;
  color: #999;
}

.recent-item > i {
  color: #ccc;
  font-size: 12px;
}

/* 设置选项 */
.settings {
  margin: 16px;
}

.setting-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:hover {
  background-color: #f9f9f9;
}

.setting-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.setting-left i {
  font-size: 16px;
  color: #666;
  width: 20px;
}

.setting-name {
  font-size: 15px;
  color: #333;
}

.setting-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-value {
  font-size: 14px;
  color: #999;
}

.setting-right i {
  color: #ccc;
  font-size: 12px;
}

/* 登录/退出按钮 */
.login-section,
.logout-section {
  margin: 20px 16px;
}

.login-btn,
.logout-btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.login-btn {
  background: #1890ff;
  color: white;
}

.login-btn:hover {
  background: #40a9ff;
}

.logout-btn {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.logout-btn:hover {
  background: #ff4d4f;
  color: white;
}
</style>
