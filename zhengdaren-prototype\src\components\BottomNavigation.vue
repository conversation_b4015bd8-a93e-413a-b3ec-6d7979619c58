<template>
  <div class="bottom-nav">
    <router-link 
      v-for="item in navItems" 
      :key="item.name"
      :to="item.path" 
      class="nav-item"
      :class="{ active: $route.path === item.path }"
    >
      <i :class="item.icon"></i>
      <span class="nav-text">{{ item.name }}</span>
    </router-link>
  </div>
</template>

<script>
export default {
  name: 'BottomNavigation',
  data() {
    return {
      navItems: [
        {
          name: '首页',
          path: '/',
          icon: 'fas fa-home'
        },
        {
          name: '发现',
          path: '/discover',
          icon: 'fas fa-search'
        },
        {
          name: '行业',
          path: '/industry',
          icon: 'fas fa-industry'
        },
        {
          name: '问答',
          path: '/qa',
          icon: 'fas fa-comments'
        },
        {
          name: '我的',
          path: '/profile',
          icon: 'fas fa-user'
        }
      ]
    }
  }
}
</script>

<style scoped>
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 375px;
  height: 60px;
  background: white;
  border-top: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  z-index: 1000;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: #999;
  transition: color 0.3s;
  padding: 6px 0;
}

.nav-item.active {
  color: #1890ff;
}

.nav-item i {
  font-size: 18px;
  margin-bottom: 2px;
}

.nav-text {
  font-size: 11px;
  line-height: 1;
}
</style>
