<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部导航 -->
      <div class="header">
        <i class="fas fa-arrow-left back-btn" @click="goBack"></i>
        <h2>行业政策</h2>
        <div class="header-actions">
          <i class="fas fa-search" @click="goToSearch"></i>
        </div>
      </div>

      <!-- 行业选择 -->
      <div class="industry-selector">
        <div class="selector-tabs">
          <div 
            class="selector-tab" 
            :class="{ active: activeIndustry === industry.key }"
            v-for="industry in industries" 
            :key="industry.key"
            @click="selectIndustry(industry.key)"
          >
            <i :class="industry.icon"></i>
            <span>{{ industry.name }}</span>
          </div>
        </div>
      </div>

      <!-- 行业概览 -->
      <div class="industry-overview" v-if="currentIndustry">
        <div class="overview-card">
          <div class="overview-header">
            <div class="industry-info">
              <i :class="currentIndustry.icon" class="industry-icon"></i>
              <div class="industry-details">
                <h3>{{ currentIndustry.name }}</h3>
                <p>{{ currentIndustry.description }}</p>
              </div>
            </div>
            <div class="industry-stats">
              <div class="stat-item">
                <span class="stat-number">{{ currentIndustry.policyCount }}</span>
                <span class="stat-label">相关政策</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ currentIndustry.hotCount }}</span>
                <span class="stat-label">热门政策</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 政策分类 -->
      <div class="policy-categories" v-if="currentIndustry">
        <h3 class="section-title">政策分类</h3>
        <div class="category-grid">
          <div 
            class="category-item" 
            v-for="category in currentIndustry.categories" 
            :key="category.id"
            @click="goToCategoryPolicies(category)"
          >
            <div class="category-icon" :style="{ backgroundColor: category.color }">
              <i :class="category.icon"></i>
            </div>
            <div class="category-info">
              <h4>{{ category.name }}</h4>
              <p>{{ category.count }}项政策</p>
            </div>
            <div class="category-badge" v-if="category.isHot">HOT</div>
          </div>
        </div>
      </div>

      <!-- 热门政策 -->
      <div class="hot-policies" v-if="currentIndustry">
        <div class="section-header">
          <h3 class="section-title">热门政策</h3>
          <span class="more-link" @click="viewAllPolicies">查看全部</span>
        </div>
        <div class="policy-list">
          <div 
            class="policy-card" 
            v-for="policy in currentIndustry.hotPolicies" 
            :key="policy.id"
            @click="goToPolicyDetail(policy)"
          >
            <div class="policy-header">
              <h4 class="policy-title">{{ policy.title }}</h4>
              <div class="policy-level" :class="policy.level">
                {{ getLevelText(policy.level) }}
              </div>
            </div>
            <p class="policy-summary">{{ policy.summary }}</p>
            <div class="policy-benefits">
              <div class="benefit-item" v-for="benefit in policy.benefits.slice(0, 2)" :key="benefit">
                <i class="fas fa-gift"></i>
                <span>{{ benefit }}</span>
              </div>
            </div>
            <div class="policy-meta">
              <span class="meta-item">
                <i class="fas fa-calendar"></i>
                {{ policy.publishDate }}
              </span>
              <span class="meta-item">
                <i class="fas fa-eye"></i>
                {{ policy.views }}
              </span>
              <span class="meta-item">
                <i class="fas fa-fire"></i>
                {{ policy.applyCount }}人申请
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 政策工具 -->
      <div class="policy-tools" v-if="currentIndustry">
        <h3 class="section-title">专业工具</h3>
        <div class="tools-grid">
          <div 
            class="tool-item" 
            v-for="tool in currentIndustry.tools" 
            :key="tool.id"
            @click="useTool(tool)"
          >
            <div class="tool-icon">
              <i :class="tool.icon"></i>
            </div>
            <div class="tool-info">
              <h4>{{ tool.name }}</h4>
              <p>{{ tool.description }}</p>
            </div>
            <div class="tool-badge" v-if="tool.isPremium">VIP</div>
          </div>
        </div>
      </div>

      <!-- 成功案例 -->
      <div class="success-cases" v-if="currentIndustry">
        <div class="section-header">
          <h3 class="section-title">成功案例</h3>
          <span class="more-link" @click="viewAllCases">更多案例</span>
        </div>
        <div class="cases-list">
          <div 
            class="case-item" 
            v-for="case_ in currentIndustry.cases" 
            :key="case_.id"
            @click="viewCaseDetail(case_)"
          >
            <div class="case-header">
              <div class="company-info">
                <h4>{{ case_.companyName }}</h4>
                <span class="company-type">{{ case_.companyType }}</span>
              </div>
              <div class="case-amount">{{ case_.amount }}</div>
            </div>
            <p class="case-description">{{ case_.description }}</p>
            <div class="case-tags">
              <span class="tag" v-for="tag in case_.tags" :key="tag">{{ tag }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'Industry',
  components: {
    BottomNavigation
  },
  data() {
    return {
      activeIndustry: 'manufacturing',
      industries: [
        {
          key: 'manufacturing',
          name: '制造业',
          icon: 'fas fa-industry',
          description: '智能制造、数字化转型、设备更新改造等政策服务',
          policyCount: 156,
          hotCount: 23,
          categories: [
            {
              id: 1,
              name: '智能制造',
              icon: 'fas fa-robot',
              color: '#1890ff',
              count: 45,
              isHot: true
            },
            {
              id: 2,
              name: '数字化转型',
              icon: 'fas fa-digital-tachograph',
              color: '#52c41a',
              count: 38
            },
            {
              id: 3,
              name: '设备更新',
              icon: 'fas fa-tools',
              color: '#fa8c16',
              count: 32
            },
            {
              id: 4,
              name: '绿色制造',
              icon: 'fas fa-leaf',
              color: '#13c2c2',
              count: 28
            },
            {
              id: 5,
              name: '技术改造',
              icon: 'fas fa-cogs',
              color: '#722ed1',
              count: 13
            }
          ],
          hotPolicies: [
            {
              id: 1,
              title: '制造业数字化转型专项资金支持政策',
              summary: '支持制造业企业开展数字化、网络化、智能化改造，最高补贴500万元',
              level: 'national',
              benefits: ['最高500万补贴', '税收减免', '优先审批'],
              publishDate: '2024-01-15',
              views: 2580,
              applyCount: 156
            },
            {
              id: 2,
              title: '智能制造示范工厂建设扶持办法',
              summary: '鼓励企业建设智能制造示范工厂，提供全方位政策支持',
              level: 'provincial',
              benefits: ['建设补贴', '运营支持', '示范奖励'],
              publishDate: '2024-01-12',
              views: 1920,
              applyCount: 89
            }
          ],
          tools: [
            {
              id: 1,
              name: '补贴计算器',
              description: '智能计算各类制造业补贴金额',
              icon: 'fas fa-calculator',
              isPremium: false
            },
            {
              id: 2,
              name: '政策匹配度分析',
              description: '分析企业与政策的匹配程度',
              icon: 'fas fa-chart-line',
              isPremium: true
            }
          ],
          cases: [
            {
              id: 1,
              companyName: '某智能装备制造公司',
              companyType: '中型企业',
              amount: '获得补贴300万元',
              description: '通过数字化转型项目，成功申请到制造业转型升级专项资金',
              tags: ['数字化转型', '智能制造', '专项资金']
            }
          ]
        },
        {
          key: 'finance',
          name: '金融业',
          icon: 'fas fa-university',
          description: '金融科技、数字货币、绿色金融等政策解读',
          policyCount: 89,
          hotCount: 15,
          categories: [
            {
              id: 1,
              name: '金融科技',
              icon: 'fas fa-mobile-alt',
              color: '#1890ff',
              count: 25,
              isHot: true
            },
            {
              id: 2,
              name: '绿色金融',
              icon: 'fas fa-leaf',
              color: '#52c41a',
              count: 22
            },
            {
              id: 3,
              name: '普惠金融',
              icon: 'fas fa-hand-holding-usd',
              color: '#fa8c16',
              count: 18
            },
            {
              id: 4,
              name: '风险管控',
              icon: 'fas fa-shield-alt',
              color: '#f5222d',
              count: 24
            }
          ],
          hotPolicies: [
            {
              id: 3,
              title: '金融科技创新监管试点政策',
              summary: '支持金融机构开展金融科技创新业务试点',
              level: 'national',
              benefits: ['监管沙盒', '政策支持', '风险缓释'],
              publishDate: '2024-01-18',
              views: 1680,
              applyCount: 45
            }
          ],
          tools: [
            {
              id: 3,
              name: '合规检查工具',
              description: '金融业务合规性自动检查',
              icon: 'fas fa-check-circle',
              isPremium: true
            }
          ],
          cases: [
            {
              id: 2,
              companyName: '某金融科技公司',
              companyType: '创新企业',
              amount: '获得试点资格',
              description: '成功入选金融科技创新监管试点，获得业务创新支持',
              tags: ['金融科技', '监管试点', '创新业务']
            }
          ]
        },
        {
          key: 'healthcare',
          name: '医疗健康',
          icon: 'fas fa-heartbeat',
          description: '医疗器械、创新药、数字医疗等政策指导',
          policyCount: 67,
          hotCount: 12,
          categories: [
            {
              id: 1,
              name: '创新药研发',
              icon: 'fas fa-pills',
              color: '#1890ff',
              count: 20,
              isHot: true
            },
            {
              id: 2,
              name: '医疗器械',
              icon: 'fas fa-stethoscope',
              color: '#52c41a',
              count: 18
            },
            {
              id: 3,
              name: '数字医疗',
              icon: 'fas fa-laptop-medical',
              color: '#722ed1',
              count: 15
            },
            {
              id: 4,
              name: '医疗服务',
              icon: 'fas fa-hospital',
              color: '#fa8c16',
              count: 14
            }
          ],
          hotPolicies: [
            {
              id: 4,
              title: '创新药品审评审批优化政策',
              summary: '加快创新药品审评审批，支持医药创新发展',
              level: 'national',
              benefits: ['绿色通道', '优先审评', '费用减免'],
              publishDate: '2024-01-20',
              views: 1420,
              applyCount: 67
            }
          ],
          tools: [
            {
              id: 4,
              name: '审批进度查询',
              description: '实时查询药品器械审批进度',
              icon: 'fas fa-search',
              isPremium: false
            }
          ],
          cases: [
            {
              id: 3,
              companyName: '某生物医药公司',
              companyType: '高新企业',
              amount: '节省审批时间6个月',
              description: '通过创新药绿色通道，大幅缩短新药上市时间',
              tags: ['创新药', '绿色通道', '快速审批']
            }
          ]
        },
        {
          key: 'education',
          name: '教育科技',
          icon: 'fas fa-graduation-cap',
          description: '在线教育、职业培训、教育信息化政策',
          policyCount: 54,
          hotCount: 9,
          categories: [
            {
              id: 1,
              name: '在线教育',
              icon: 'fas fa-laptop',
              color: '#1890ff',
              count: 18
            },
            {
              id: 2,
              name: '职业培训',
              icon: 'fas fa-chalkboard-teacher',
              color: '#52c41a',
              count: 16,
              isHot: true
            },
            {
              id: 3,
              name: '教育信息化',
              icon: 'fas fa-desktop',
              color: '#722ed1',
              count: 12
            },
            {
              id: 4,
              name: '产教融合',
              icon: 'fas fa-handshake',
              color: '#fa8c16',
              count: 8
            }
          ],
          hotPolicies: [
            {
              id: 5,
              title: '职业技能提升行动专项资金',
              summary: '支持企业开展职工技能培训，按培训成本给予补贴',
              level: 'national',
              benefits: ['培训补贴', '税收优惠', '认证支持'],
              publishDate: '2024-01-22',
              views: 980,
              applyCount: 234
            }
          ],
          tools: [
            {
              id: 5,
              name: '培训补贴计算',
              description: '计算职业培训补贴金额',
              icon: 'fas fa-calculator',
              isPremium: false
            }
          ],
          cases: [
            {
              id: 4,
              companyName: '某职业教育机构',
              companyType: '教育机构',
              amount: '获得补贴80万元',
              description: '开展技能培训项目，获得职业技能提升专项资金支持',
              tags: ['职业培训', '技能提升', '专项资金']
            }
          ]
        }
      ]
    }
  },
  computed: {
    currentIndustry() {
      return this.industries.find(industry => industry.key === this.activeIndustry)
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToSearch() {
      this.$router.push('/discover')
    },
    selectIndustry(industryKey) {
      this.activeIndustry = industryKey
    },
    goToCategoryPolicies(category) {
      this.$router.push(`/discover?industry=${this.activeIndustry}&category=${category.id}`)
    },
    goToPolicyDetail(policy) {
      this.$router.push(`/policy/${policy.id}`)
    },
    viewAllPolicies() {
      this.$router.push(`/discover?industry=${this.activeIndustry}`)
    },
    useTool(tool) {
      if (tool.isPremium) {
        this.$router.push('/membership')
      } else {
        this.$router.push(`/tools/${tool.id}`)
      }
    },
    viewAllCases() {
      this.$router.push(`/cases?industry=${this.activeIndustry}`)
    },
    viewCaseDetail(case_) {
      this.$router.push(`/case/${case_.id}`)
    },
    getLevelText(level) {
      const levelMap = {
        'national': '国家级',
        'provincial': '省级',
        'municipal': '市级'
      }
      return levelMap[level] || '其他'
    }
  },
  mounted() {
    // 从路由参数获取行业
    if (this.$route.query.industry) {
      this.activeIndustry = this.$route.query.industry
    }
  }
}
</script>

<style scoped>
/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* 行业选择器 */
.industry-selector {
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.selector-tabs {
  display: flex;
  overflow-x: auto;
  padding: 0 16px;
}

.selector-tab {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.selector-tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.selector-tab i {
  font-size: 20px;
  margin-bottom: 4px;
}

.selector-tab span {
  font-size: 12px;
}

/* 行业概览 */
.industry-overview {
  padding: 16px;
  background: #f5f5f5;
}

.overview-card {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 12px;
  padding: 20px;
  color: white;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.industry-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.industry-icon {
  font-size: 32px;
  opacity: 0.9;
}

.industry-details h3 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 4px;
}

.industry-details p {
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}

.industry-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

/* 政策分类 */
.policy-categories {
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.category-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: transform 0.2s;
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
}

.category-item:hover {
  transform: translateY(-2px);
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.category-icon i {
  font-size: 18px;
  color: white;
}

.category-info {
  flex: 1;
}

.category-info h4 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.category-info p {
  font-size: 12px;
  color: #999;
}

.category-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 热门政策 */
.hot-policies {
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.more-link {
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
}

.policy-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.policy-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.policy-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.policy-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
}

.policy-level {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
}

.policy-level.national {
  background: #fff2e8;
  color: #fa8c16;
}

.policy-level.provincial {
  background: #e6f7ff;
  color: #1890ff;
}

.policy-level.municipal {
  background: #f6ffed;
  color: #52c41a;
}

.policy-summary {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
}

.policy-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f0f8ff;
  color: #1890ff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.benefit-item i {
  font-size: 10px;
}

.policy-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 政策工具 */
.policy-tools {
  padding: 16px;
  background: #f9f9f9;
}

.tools-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tool-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.tool-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tool-icon {
  width: 40px;
  height: 40px;
  background: #f0f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tool-icon i {
  font-size: 18px;
  color: #1890ff;
}

.tool-info {
  flex: 1;
}

.tool-info h4 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.tool-info p {
  font-size: 12px;
  color: #666;
}

.tool-badge {
  background: #722ed1;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 成功案例 */
.success-cases {
  padding: 16px;
}

.cases-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.case-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.case-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.company-info h4 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.company-type {
  font-size: 12px;
  color: #999;
}

.case-amount {
  font-size: 14px;
  font-weight: 500;
  color: #52c41a;
}

.case-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
}

.case-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.case-tags .tag {
  background: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}
</style>
