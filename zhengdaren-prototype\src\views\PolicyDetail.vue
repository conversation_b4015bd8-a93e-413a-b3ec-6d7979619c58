<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部导航 -->
      <div class="header">
        <i class="fas fa-arrow-left back-btn" @click="goBack"></i>
        <h2>政策详情</h2>
        <div class="header-actions">
          <i class="fas fa-share-alt" @click="sharePolicy"></i>
          <i :class="policy.isFavorite ? 'fas fa-heart' : 'far fa-heart'" 
             @click="toggleFavorite"
             :style="{ color: policy.isFavorite ? '#ff4d4f' : '#666' }"></i>
        </div>
      </div>

      <!-- 政策基本信息 -->
      <div class="policy-header">
        <h1 class="policy-title">{{ policy.title }}</h1>
        <div class="policy-meta">
          <div class="meta-row">
            <span class="meta-label">发布机构：</span>
            <span class="meta-value">{{ policy.publisher }}</span>
          </div>
          <div class="meta-row">
            <span class="meta-label">发布时间：</span>
            <span class="meta-value">{{ policy.publishDate }}</span>
          </div>
          <div class="meta-row">
            <span class="meta-label">有效期：</span>
            <span class="meta-value">{{ policy.validPeriod }}</span>
          </div>
          <div class="meta-row">
            <span class="meta-label">浏览量：</span>
            <span class="meta-value">{{ policy.views }}次</span>
          </div>
        </div>
        
        <div class="policy-tags">
          <span 
            class="tag" 
            :class="getTagClass(tag)" 
            v-for="tag in policy.tags" 
            :key="tag"
          >
            {{ tag }}
          </span>
        </div>
      </div>

      <!-- 功能标签页 -->
      <div class="tab-navigation">
        <div 
          class="tab-item" 
          :class="{ active: activeTab === tab.key }"
          v-for="tab in tabs" 
          :key="tab.key"
          @click="activeTab = tab.key"
        >
          <i :class="tab.icon"></i>
          {{ tab.name }}
        </div>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content">
        <!-- AI智能解读 -->
        <div v-if="activeTab === 'ai'" class="ai-interpretation">
          <div class="ai-header">
            <i class="fas fa-robot"></i>
            <h3>AI智能解读</h3>
            <span class="ai-badge">智能</span>
          </div>
          
          <div class="interpretation-section">
            <h4><i class="fas fa-lightbulb"></i> 政策要点</h4>
            <ul class="key-points">
              <li v-for="point in policy.aiInterpretation.keyPoints" :key="point">
                {{ point }}
              </li>
            </ul>
          </div>
          
          <div class="interpretation-section">
            <h4><i class="fas fa-users"></i> 适用对象</h4>
            <div class="target-audience">
              <div 
                class="audience-item" 
                v-for="audience in policy.aiInterpretation.targetAudience" 
                :key="audience.type"
              >
                <div class="audience-icon">
                  <i :class="audience.icon"></i>
                </div>
                <div class="audience-info">
                  <h5>{{ audience.type }}</h5>
                  <p>{{ audience.description }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="interpretation-section">
            <h4><i class="fas fa-coins"></i> 资金支持</h4>
            <div class="funding-info">
              <div class="funding-item" v-for="fund in policy.aiInterpretation.funding" :key="fund.type">
                <span class="funding-type">{{ fund.type }}</span>
                <span class="funding-amount">{{ fund.amount }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 原文内容 -->
        <div v-if="activeTab === 'content'" class="policy-content">
          <div class="content-text" v-html="policy.content"></div>
        </div>

        <!-- 申请指导 -->
        <div v-if="activeTab === 'guide'" class="application-guide">
          <div class="guide-section">
            <h4><i class="fas fa-list-ol"></i> 申请流程</h4>
            <div class="process-steps">
              <div 
                class="step-item" 
                v-for="(step, index) in policy.applicationGuide.steps" 
                :key="index"
              >
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-content">
                  <h5>{{ step.title }}</h5>
                  <p>{{ step.description }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="guide-section">
            <h4><i class="fas fa-file-alt"></i> 申请材料</h4>
            <div class="materials-list">
              <div 
                class="material-item" 
                v-for="material in policy.applicationGuide.materials" 
                :key="material.name"
              >
                <i class="fas fa-check-circle"></i>
                <span class="material-name">{{ material.name }}</span>
                <span class="material-note" v-if="material.note">{{ material.note }}</span>
              </div>
            </div>
          </div>
          
          <div class="guide-section">
            <h4><i class="fas fa-clock"></i> 办理时限</h4>
            <div class="timeline-info">
              <p>{{ policy.applicationGuide.timeline }}</p>
            </div>
          </div>
          
          <div class="guide-actions">
            <button class="btn btn-primary" @click="startApplication">
              <i class="fas fa-edit"></i>
              开始申请
            </button>
            <button class="btn btn-secondary" @click="consultExpert">
              <i class="fas fa-user-tie"></i>
              专家咨询
            </button>
          </div>
        </div>

        <!-- 相关政策 -->
        <div v-if="activeTab === 'related'" class="related-policies">
          <div 
            class="related-item" 
            v-for="relatedPolicy in policy.relatedPolicies" 
            :key="relatedPolicy.id"
            @click="goToPolicyDetail(relatedPolicy.id)"
          >
            <h4 class="related-title">{{ relatedPolicy.title }}</h4>
            <p class="related-summary">{{ relatedPolicy.summary }}</p>
            <div class="related-meta">
              <span class="related-date">{{ relatedPolicy.publishDate }}</span>
              <span class="related-views">{{ relatedPolicy.views }}次浏览</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <button class="action-btn" @click="toggleFavorite">
        <i :class="policy.isFavorite ? 'fas fa-heart' : 'far fa-heart'"></i>
        {{ policy.isFavorite ? '已收藏' : '收藏' }}
      </button>
      <button class="action-btn" @click="sharePolicy">
        <i class="fas fa-share"></i>
        分享
      </button>
      <button class="action-btn primary" @click="startApplication">
        <i class="fas fa-edit"></i>
        立即申请
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PolicyDetail',
  data() {
    return {
      activeTab: 'ai',
      tabs: [
        { key: 'ai', name: 'AI解读', icon: 'fas fa-robot' },
        { key: 'content', name: '原文', icon: 'fas fa-file-text' },
        { key: 'guide', name: '申请指导', icon: 'fas fa-clipboard-list' },
        { key: 'related', name: '相关政策', icon: 'fas fa-link' }
      ],
      policy: {
        id: 1,
        title: '关于支持中小企业数字化转型的若干措施',
        publisher: '工业和信息化部',
        publishDate: '2024-01-15',
        validPeriod: '2024-01-15 至 2026-12-31',
        views: 1250,
        isFavorite: false,
        tags: ['企业扶持', '数字化', '中小企业', '资金补贴'],
        content: `
          <h3>第一章 总则</h3>
          <p>第一条 为加快推进中小企业数字化转型，提升企业竞争力，根据《中华人民共和国中小企业促进法》等法律法规，制定本措施。</p>
          <p>第二条 本措施所称中小企业数字化转型，是指中小企业运用数字技术对企业的业务流程、组织架构、企业文化等进行改造升级的过程。</p>
          
          <h3>第二章 支持措施</h3>
          <p>第三条 对符合条件的中小企业数字化转型项目，给予最高100万元的资金补贴。</p>
          <p>第四条 建立中小企业数字化转型服务平台，为企业提供技术咨询、人才培训等服务。</p>
        `,
        aiInterpretation: {
          keyPoints: [
            '支持中小企业进行数字化转型升级',
            '提供最高100万元资金补贴',
            '建立专门的服务平台提供技术支持',
            '重点支持制造业、服务业等传统行业',
            '有效期至2026年12月31日'
          ],
          targetAudience: [
            {
              type: '中小制造企业',
              icon: 'fas fa-industry',
              description: '从事制造业的中小企业，员工人数不超过300人'
            },
            {
              type: '科技服务企业',
              icon: 'fas fa-laptop-code',
              description: '提供技术服务的中小企业，年营业收入不超过5000万元'
            },
            {
              type: '传统服务业',
              icon: 'fas fa-store',
              description: '餐饮、零售等传统服务业企业'
            }
          ],
          funding: [
            { type: '设备购置补贴', amount: '最高50万元' },
            { type: '软件开发补贴', amount: '最高30万元' },
            { type: '人才培训补贴', amount: '最高20万元' }
          ]
        },
        applicationGuide: {
          steps: [
            {
              title: '在线申报',
              description: '登录政务服务平台，填写申请表格并上传相关材料'
            },
            {
              title: '初步审核',
              description: '相关部门对申请材料进行初步审核，5个工作日内完成'
            },
            {
              title: '现场核查',
              description: '通过初审的项目将安排专家进行现场核查'
            },
            {
              title: '公示公告',
              description: '拟支持项目进行7天公示，接受社会监督'
            },
            {
              title: '资金拨付',
              description: '公示无异议后，按程序拨付补贴资金'
            }
          ],
          materials: [
            { name: '企业营业执照', note: '原件及复印件' },
            { name: '数字化转型方案', note: '详细说明转型计划' },
            { name: '项目预算表', note: '包含设备、软件、人员等费用' },
            { name: '企业财务报表', note: '近两年审计报告' },
            { name: '技术合同', note: '与技术服务商签订的合同' }
          ],
          timeline: '从申请提交到资金拨付，整个流程约需30-45个工作日'
        },
        relatedPolicies: [
          {
            id: 2,
            title: '中小企业数字化转型指南',
            summary: '为中小企业数字化转型提供具体指导和建议',
            publishDate: '2024-01-10',
            views: 890
          },
          {
            id: 3,
            title: '制造业数字化转型行动计划',
            summary: '推动制造业企业加快数字化转型步伐',
            publishDate: '2024-01-08',
            views: 1120
          }
        ]
      }
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    toggleFavorite() {
      this.policy.isFavorite = !this.policy.isFavorite
      // 实际项目中这里会调用API
    },
    sharePolicy() {
      // 分享功能
      if (navigator.share) {
        navigator.share({
          title: this.policy.title,
          text: this.policy.title,
          url: window.location.href
        })
      } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
          alert('链接已复制到剪贴板')
        })
      }
    },
    startApplication() {
      this.$router.push(`/apply/${this.policy.id}`)
    },
    consultExpert() {
      this.$router.push('/expert-consultation')
    },
    goToPolicyDetail(id) {
      this.$router.push(`/policy/${id}`)
    },
    getTagClass(tag) {
      const tagMap = {
        '企业扶持': 'tag-primary',
        '数字化': 'tag-success',
        '中小企业': 'tag-primary',
        '资金补贴': 'tag-warning'
      }
      return tagMap[tag] || ''
    }
  },
  mounted() {
    // 根据路由参数获取政策详情
    const policyId = this.$route.params.id
    // 实际项目中这里会调用API获取政策详情
    console.log('Policy ID:', policyId)
  }
}
</script>

<style scoped>
/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* 政策头部信息 */
.policy-header {
  padding: 20px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.policy-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16px;
}

.policy-meta {
  margin-bottom: 16px;
}

.meta-row {
  display: flex;
  margin-bottom: 8px;
}

.meta-label {
  font-size: 14px;
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.meta-value {
  font-size: 14px;
  color: #333;
}

.policy-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab-item.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.tab-item i {
  font-size: 16px;
  margin-bottom: 4px;
}

/* 标签页内容 */
.tab-content {
  padding: 16px;
  background: #f5f5f5;
  min-height: 400px;
}

/* AI解读 */
.ai-interpretation {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.ai-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.ai-header i {
  font-size: 20px;
}

.ai-header h3 {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
}

.ai-badge {
  background: rgba(255,255,255,0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.interpretation-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.interpretation-section:last-child {
  border-bottom: none;
}

.interpretation-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.interpretation-section h4 i {
  color: #1890ff;
}

.key-points {
  list-style: none;
  padding: 0;
}

.key-points li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.key-points li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #1890ff;
  font-weight: bold;
}

.target-audience {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.audience-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
}

.audience-icon {
  width: 36px;
  height: 36px;
  background: #e6f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.audience-icon i {
  font-size: 16px;
  color: #1890ff;
}

.audience-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.audience-info p {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.funding-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.funding-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9f9f9;
  border-radius: 6px;
}

.funding-type {
  font-size: 14px;
  color: #333;
}

.funding-amount {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

/* 政策内容 */
.policy-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.content-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.content-text h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 20px 0 12px 0;
  color: #333;
}

.content-text p {
  margin-bottom: 12px;
}

/* 申请指导 */
.application-guide {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.guide-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.guide-section:last-child {
  border-bottom: none;
}

.guide-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.guide-section h4 i {
  color: #1890ff;
}

.process-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.step-content h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.step-content p {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.materials-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.material-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.material-item i {
  color: #52c41a;
  font-size: 12px;
}

.material-name {
  color: #333;
}

.material-note {
  color: #999;
  font-size: 12px;
}

.timeline-info p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.guide-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.guide-actions .btn {
  flex: 1;
  padding: 10px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 相关政策 */
.related-policies {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.related-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.related-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.related-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.related-summary {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8px;
}

.related-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 375px;
  display: flex;
  background: white;
  border-top: 1px solid #f0f0f0;
  padding: 12px 16px;
  gap: 12px;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 12px;
}

.action-btn:hover {
  background: #f0f0f0;
}

.action-btn.primary {
  background: #1890ff;
  color: white;
}

.action-btn.primary:hover {
  background: #40a9ff;
}

.action-btn i {
  font-size: 16px;
}
</style>
