<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部导航 -->
      <div class="header">
        <i class="fas fa-arrow-left back-btn" @click="goBack"></i>
        <h2>{{ currentTool.name }}</h2>
        <div class="header-actions">
          <i class="fas fa-share-alt" @click="shareTool"></i>
        </div>
      </div>

      <!-- 工具介绍 -->
      <div class="tool-intro">
        <div class="intro-card">
          <div class="tool-icon">
            <i :class="currentTool.icon"></i>
          </div>
          <div class="tool-info">
            <h3>{{ currentTool.name }}</h3>
            <p>{{ currentTool.description }}</p>
          </div>
          <div class="tool-badge" v-if="currentTool.isPremium">VIP</div>
        </div>
      </div>

      <!-- 补贴计算器 -->
      <div class="calculator-section" v-if="toolType === 'subsidy-calculator'">
        <div class="calculator-form">
          <h4>企业基本信息</h4>
          
          <div class="form-group">
            <label>企业类型</label>
            <select v-model="calculatorData.companyType">
              <option value="">请选择企业类型</option>
              <option value="micro">微型企业</option>
              <option value="small">小型企业</option>
              <option value="medium">中型企业</option>
              <option value="large">大型企业</option>
            </select>
          </div>

          <div class="form-group">
            <label>所属行业</label>
            <select v-model="calculatorData.industry">
              <option value="">请选择行业</option>
              <option value="manufacturing">制造业</option>
              <option value="software">软件业</option>
              <option value="service">服务业</option>
              <option value="agriculture">农业</option>
            </select>
          </div>

          <div class="form-group">
            <label>年营业收入（万元）</label>
            <input 
              type="number" 
              v-model="calculatorData.revenue" 
              placeholder="请输入年营业收入"
            >
          </div>

          <div class="form-group">
            <label>员工人数</label>
            <input 
              type="number" 
              v-model="calculatorData.employeeCount" 
              placeholder="请输入员工人数"
            >
          </div>

          <div class="form-group">
            <label>项目投资金额（万元）</label>
            <input 
              type="number" 
              v-model="calculatorData.investmentAmount" 
              placeholder="请输入项目投资金额"
            >
          </div>

          <div class="form-group">
            <label>项目类型</label>
            <div class="checkbox-group">
              <label class="checkbox-item" v-for="type in projectTypes" :key="type.value">
                <input 
                  type="checkbox" 
                  :value="type.value" 
                  v-model="calculatorData.projectTypes"
                >
                <span>{{ type.label }}</span>
              </label>
            </div>
          </div>

          <button class="calculate-btn" @click="calculateSubsidy" :disabled="!canCalculate">
            <i class="fas fa-calculator"></i>
            计算补贴金额
          </button>
        </div>

        <!-- 计算结果 -->
        <div class="calculation-result" v-if="calculationResult">
          <div class="result-header">
            <h4>计算结果</h4>
            <div class="result-confidence">
              <span>准确度：{{ calculationResult.confidence }}%</span>
            </div>
          </div>

          <div class="result-summary">
            <div class="total-subsidy">
              <span class="label">预计可获得补贴</span>
              <span class="amount">¥{{ calculationResult.totalAmount.toLocaleString() }}</span>
            </div>
          </div>

          <div class="subsidy-breakdown">
            <h5>补贴明细</h5>
            <div class="breakdown-list">
              <div 
                class="breakdown-item" 
                v-for="item in calculationResult.breakdown" 
                :key="item.type"
              >
                <div class="item-info">
                  <h6>{{ item.name }}</h6>
                  <p>{{ item.description }}</p>
                </div>
                <div class="item-amount">¥{{ item.amount.toLocaleString() }}</div>
              </div>
            </div>
          </div>

          <div class="result-actions">
            <button class="action-btn secondary" @click="saveResult">
              <i class="fas fa-save"></i>
              保存结果
            </button>
            <button class="action-btn primary" @click="applyForPolicies">
              <i class="fas fa-paper-plane"></i>
              申请相关政策
            </button>
          </div>
        </div>
      </div>

      <!-- 政策匹配度分析 -->
      <div class="matching-section" v-if="toolType === 'policy-matching'">
        <div class="matching-form">
          <h4>企业信息分析</h4>
          
          <div class="form-group">
            <label>企业统一社会信用代码</label>
            <input 
              type="text" 
              v-model="matchingData.creditCode" 
              placeholder="请输入18位统一社会信用代码"
            >
          </div>

          <div class="form-group">
            <label>主营业务描述</label>
            <textarea 
              v-model="matchingData.businessDescription" 
              placeholder="请详细描述企业主营业务"
              rows="3"
            ></textarea>
          </div>

          <div class="form-group">
            <label>发展阶段</label>
            <select v-model="matchingData.developmentStage">
              <option value="">请选择发展阶段</option>
              <option value="startup">初创期</option>
              <option value="growth">成长期</option>
              <option value="mature">成熟期</option>
              <option value="transformation">转型期</option>
            </select>
          </div>

          <div class="form-group">
            <label>技术水平</label>
            <div class="radio-group">
              <label class="radio-item" v-for="level in techLevels" :key="level.value">
                <input 
                  type="radio" 
                  :value="level.value" 
                  v-model="matchingData.techLevel"
                >
                <span>{{ level.label }}</span>
              </label>
            </div>
          </div>

          <button class="analyze-btn" @click="analyzeMatching" :disabled="!canAnalyze">
            <i class="fas fa-chart-line"></i>
            开始分析
          </button>
        </div>

        <!-- 分析结果 -->
        <div class="matching-result" v-if="matchingResult">
          <div class="result-overview">
            <h4>政策匹配分析报告</h4>
            <div class="overall-score">
              <div class="score-circle">
                <span class="score">{{ matchingResult.overallScore }}</span>
                <span class="score-label">综合匹配度</span>
              </div>
            </div>
          </div>

          <div class="matching-policies">
            <h5>推荐政策（按匹配度排序）</h5>
            <div class="policy-list">
              <div 
                class="policy-match-item" 
                v-for="policy in matchingResult.policies" 
                :key="policy.id"
                @click="viewPolicyDetail(policy)"
              >
                <div class="policy-info">
                  <h6>{{ policy.title }}</h6>
                  <p>{{ policy.summary }}</p>
                  <div class="policy-tags">
                    <span class="tag" v-for="tag in policy.tags" :key="tag">{{ tag }}</span>
                  </div>
                </div>
                <div class="match-score">
                  <div class="score-bar">
                    <div class="score-fill" :style="{ width: policy.matchScore + '%' }"></div>
                  </div>
                  <span class="score-text">{{ policy.matchScore }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="tool-instructions">
        <h4>使用说明</h4>
        <div class="instruction-list">
          <div class="instruction-item" v-for="instruction in currentTool.instructions" :key="instruction">
            <i class="fas fa-info-circle"></i>
            <span>{{ instruction }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'PolicyTool',
  components: {
    BottomNavigation
  },
  data() {
    return {
      toolType: 'subsidy-calculator', // 从路由参数获取
      calculatorData: {
        companyType: '',
        industry: '',
        revenue: '',
        employeeCount: '',
        investmentAmount: '',
        projectTypes: []
      },
      matchingData: {
        creditCode: '',
        businessDescription: '',
        developmentStage: '',
        techLevel: ''
      },
      calculationResult: null,
      matchingResult: null,
      projectTypes: [
        { value: 'digitalization', label: '数字化转型' },
        { value: 'automation', label: '自动化改造' },
        { value: 'rd', label: '研发创新' },
        { value: 'green', label: '绿色制造' },
        { value: 'training', label: '人才培训' }
      ],
      techLevels: [
        { value: 'basic', label: '基础水平' },
        { value: 'intermediate', label: '中等水平' },
        { value: 'advanced', label: '先进水平' },
        { value: 'leading', label: '领先水平' }
      ],
      tools: {
        'subsidy-calculator': {
          name: '补贴计算器',
          icon: 'fas fa-calculator',
          description: '智能计算各类制造业补贴金额，帮助企业了解可获得的政策支持',
          isPremium: false,
          instructions: [
            '请如实填写企业基本信息，确保计算结果准确性',
            '投资金额请填写项目总投资，不含土地费用',
            '可同时选择多个项目类型，系统会综合计算',
            '计算结果仅供参考，实际补贴以政府审批为准'
          ]
        },
        'policy-matching': {
          name: '政策匹配度分析',
          icon: 'fas fa-chart-line',
          description: '基于企业特征分析政策匹配程度，精准推荐适合的政策',
          isPremium: true,
          instructions: [
            '请提供准确的企业信用代码以获取企业信息',
            '详细描述主营业务有助于提高匹配准确度',
            '系统会根据企业画像智能匹配相关政策',
            '匹配度越高的政策申请成功率越大'
          ]
        }
      }
    }
  },
  computed: {
    currentTool() {
      return this.tools[this.toolType] || this.tools['subsidy-calculator']
    },
    canCalculate() {
      return this.calculatorData.companyType && 
             this.calculatorData.industry && 
             this.calculatorData.revenue && 
             this.calculatorData.employeeCount
    },
    canAnalyze() {
      return this.matchingData.creditCode && 
             this.matchingData.businessDescription && 
             this.matchingData.developmentStage
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    shareTool() {
      // 分享工具
      if (navigator.share) {
        navigator.share({
          title: this.currentTool.name,
          text: this.currentTool.description,
          url: window.location.href
        })
      } else {
        navigator.clipboard.writeText(window.location.href).then(() => {
          alert('链接已复制到剪贴板')
        })
      }
    },
    calculateSubsidy() {
      // 模拟计算逻辑
      const baseAmount = parseInt(this.calculatorData.investmentAmount) || 0
      let totalAmount = 0
      const breakdown = []

      // 根据企业类型计算基础补贴
      const typeMultiplier = {
        'micro': 0.3,
        'small': 0.25,
        'medium': 0.2,
        'large': 0.15
      }
      const baseSubsidy = baseAmount * (typeMultiplier[this.calculatorData.companyType] || 0.2)
      
      if (baseSubsidy > 0) {
        breakdown.push({
          type: 'base',
          name: '基础投资补贴',
          description: '根据投资金额和企业规模计算的基础补贴',
          amount: Math.min(baseSubsidy * 10000, 1000000) // 最高100万
        })
        totalAmount += breakdown[breakdown.length - 1].amount
      }

      // 根据项目类型计算专项补贴
      this.calculatorData.projectTypes.forEach(type => {
        const projectSubsidies = {
          'digitalization': { name: '数字化转型补贴', amount: 200000 },
          'automation': { name: '自动化改造补贴', amount: 150000 },
          'rd': { name: '研发创新补贴', amount: 300000 },
          'green': { name: '绿色制造补贴', amount: 180000 },
          'training': { name: '人才培训补贴', amount: 50000 }
        }
        
        if (projectSubsidies[type]) {
          breakdown.push({
            type: type,
            name: projectSubsidies[type].name,
            description: '专项政策支持补贴',
            amount: projectSubsidies[type].amount
          })
          totalAmount += projectSubsidies[type].amount
        }
      })

      this.calculationResult = {
        totalAmount: totalAmount,
        confidence: 85,
        breakdown: breakdown
      }
    },
    analyzeMatching() {
      // 模拟分析逻辑
      setTimeout(() => {
        this.matchingResult = {
          overallScore: 78,
          policies: [
            {
              id: 1,
              title: '制造业数字化转型专项资金',
              summary: '支持制造业企业开展数字化、网络化、智能化改造',
              tags: ['数字化转型', '制造业', '专项资金'],
              matchScore: 92
            },
            {
              id: 2,
              title: '中小企业技术改造补贴',
              summary: '对中小企业技术改造项目给予资金补贴',
              tags: ['技术改造', '中小企业', '补贴'],
              matchScore: 85
            },
            {
              id: 3,
              title: '高新技术企业认定',
              summary: '符合条件的企业可申请高新技术企业认定',
              tags: ['高新技术', '企业认定', '税收优惠'],
              matchScore: 72
            }
          ]
        }
      }, 2000)
    },
    saveResult() {
      // 保存计算结果
      alert('结果已保存到个人中心')
    },
    applyForPolicies() {
      // 跳转到政策申请页面
      this.$router.push('/apply')
    },
    viewPolicyDetail(policy) {
      this.$router.push(`/policy/${policy.id}`)
    }
  },
  mounted() {
    // 从路由参数获取工具类型
    const toolId = this.$route.params.id
    if (toolId === '2') {
      this.toolType = 'policy-matching'
    }
  }
}
</script>

<style scoped>
/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* 工具介绍 */
.tool-intro {
  padding: 16px;
  background: #f5f5f5;
}

.intro-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.tool-icon {
  width: 50px;
  height: 50px;
  background: #f0f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tool-icon i {
  font-size: 24px;
  color: #1890ff;
}

.tool-info {
  flex: 1;
}

.tool-info h3 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.tool-info p {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.tool-badge {
  background: #722ed1;
  color: white;
  font-size: 10px;
  padding: 4px 8px;
  border-radius: 8px;
}

/* 计算器表单 */
.calculator-section,
.matching-section {
  padding: 16px;
}

.calculator-form,
.matching-form {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
}

.calculator-form h4,
.matching-form h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #1890ff;
}

.checkbox-group,
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-item,
.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  cursor: pointer;
}

.calculate-btn,
.analyze-btn {
  width: 100%;
  padding: 12px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.calculate-btn:hover,
.analyze-btn:hover {
  background: #40a9ff;
}

.calculate-btn:disabled,
.analyze-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

/* 计算结果 */
.calculation-result,
.matching-result {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-header h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.result-confidence {
  font-size: 12px;
  color: #52c41a;
  background: #f6ffed;
  padding: 4px 8px;
  border-radius: 12px;
}

.result-summary {
  text-align: center;
  margin-bottom: 20px;
}

.total-subsidy {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.total-subsidy .label {
  font-size: 14px;
  color: #666;
}

.total-subsidy .amount {
  font-size: 28px;
  font-weight: 600;
  color: #52c41a;
}

.subsidy-breakdown {
  margin-bottom: 20px;
}

.subsidy-breakdown h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.breakdown-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 6px;
}

.item-info h6 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.item-info p {
  font-size: 12px;
  color: #666;
}

.item-amount {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

.result-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
}

.action-btn.primary {
  background: #1890ff;
  color: white;
}

/* 匹配分析结果 */
.result-overview {
  text-align: center;
  margin-bottom: 20px;
}

.overall-score {
  margin-top: 16px;
}

.score-circle {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 4px solid #1890ff;
  border-radius: 50%;
}

.score {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
}

.score-label {
  font-size: 10px;
  color: #666;
}

.matching-policies h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.policy-match-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.policy-match-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24,144,255,0.1);
}

.policy-info {
  flex: 1;
  margin-right: 16px;
}

.policy-info h6 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.policy-info p {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.policy-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.policy-tags .tag {
  background: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.match-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-bar {
  width: 60px;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background: #52c41a;
  transition: width 0.3s;
}

.score-text {
  font-size: 12px;
  font-weight: 500;
  color: #52c41a;
}

/* 使用说明 */
.tool-instructions {
  padding: 16px;
}

.tool-instructions h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.instruction-list {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.instruction-item:last-child {
  margin-bottom: 0;
}

.instruction-item i {
  color: #1890ff;
  margin-top: 2px;
  flex-shrink: 0;
}
</style>
