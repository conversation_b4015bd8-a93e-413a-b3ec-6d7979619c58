<template>
  <div class="container">
    <div class="page-content">
      <!-- 顶部导航 -->
      <div class="header">
        <i class="fas fa-arrow-left back-btn" @click="goBack"></i>
        <h2>企业政策画像</h2>
        <div class="header-actions">
          <i class="fas fa-sync-alt" @click="refreshProfile"></i>
        </div>
      </div>

      <!-- 企业基本信息 -->
      <div class="company-basic-info">
        <div class="info-card">
          <div class="company-header">
            <div class="company-logo">
              <i class="fas fa-building"></i>
            </div>
            <div class="company-details">
              <h3>{{ companyInfo.name }}</h3>
              <p>{{ companyInfo.industry }} | {{ companyInfo.scale }}</p>
              <div class="company-tags">
                <span class="tag" v-for="tag in companyInfo.tags" :key="tag">{{ tag }}</span>
              </div>
            </div>
            <div class="profile-score">
              <div class="score-circle">
                <span class="score">{{ profileScore }}</span>
                <span class="score-label">画像完整度</span>
              </div>
            </div>
          </div>
          
          <div class="company-stats">
            <div class="stat-item">
              <span class="stat-number">{{ companyInfo.establishedYear }}</span>
              <span class="stat-label">成立年份</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ companyInfo.employees }}</span>
              <span class="stat-label">员工数量</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ companyInfo.revenue }}万</span>
              <span class="stat-label">年营收</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ companyInfo.rdRatio }}%</span>
              <span class="stat-label">研发占比</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 政策匹配分析 -->
      <div class="policy-matching-analysis">
        <div class="section-header">
          <h4>政策匹配分析</h4>
          <span class="analysis-time">{{ analysisTime }}</span>
        </div>
        
        <div class="matching-overview">
          <div class="overview-stats">
            <div class="overview-item">
              <div class="overview-number">{{ matchingStats.totalPolicies }}</div>
              <div class="overview-label">可申请政策</div>
            </div>
            <div class="overview-item">
              <div class="overview-number">{{ matchingStats.highMatch }}</div>
              <div class="overview-label">高匹配度</div>
            </div>
            <div class="overview-item">
              <div class="overview-number">{{ matchingStats.potentialValue }}万</div>
              <div class="overview-label">潜在价值</div>
            </div>
          </div>
        </div>

        <div class="matching-categories">
          <div 
            class="category-item" 
            v-for="category in policyCategories" 
            :key="category.id"
            @click="viewCategoryDetail(category)"
          >
            <div class="category-header">
              <div class="category-icon" :style="{ backgroundColor: category.color }">
                <i :class="category.icon"></i>
              </div>
              <div class="category-info">
                <h5>{{ category.name }}</h5>
                <p>{{ category.description }}</p>
              </div>
              <div class="category-match">
                <div class="match-score">{{ category.matchScore }}%</div>
                <div class="policy-count">{{ category.policyCount }}项政策</div>
              </div>
            </div>
            
            <div class="category-policies">
              <div 
                class="policy-preview" 
                v-for="policy in category.topPolicies" 
                :key="policy.id"
                @click.stop="viewPolicyDetail(policy)"
              >
                <div class="policy-title">{{ policy.title }}</div>
                <div class="policy-value">{{ policy.maxValue }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 企业特征分析 -->
      <div class="company-characteristics">
        <h4>企业特征分析</h4>
        <div class="characteristics-grid">
          <div class="characteristic-item" v-for="char in characteristics" :key="char.id">
            <div class="char-header">
              <div class="char-icon">
                <i :class="char.icon"></i>
              </div>
              <h5>{{ char.name }}</h5>
            </div>
            <div class="char-content">
              <div class="char-score">
                <div class="score-bar">
                  <div class="score-fill" :style="{ width: char.score + '%', backgroundColor: char.color }"></div>
                </div>
                <span class="score-text">{{ char.score }}%</span>
              </div>
              <p class="char-description">{{ char.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 政策申请建议 -->
      <div class="application-suggestions">
        <h4>申请建议</h4>
        <div class="suggestion-list">
          <div 
            class="suggestion-item" 
            :class="suggestion.priority"
            v-for="suggestion in applicationSuggestions" 
            :key="suggestion.id"
          >
            <div class="suggestion-header">
              <div class="suggestion-icon">
                <i :class="suggestion.icon"></i>
              </div>
              <div class="suggestion-info">
                <h5>{{ suggestion.title }}</h5>
                <p>{{ suggestion.description }}</p>
              </div>
              <div class="suggestion-priority">
                {{ getPriorityText(suggestion.priority) }}
              </div>
            </div>
            <div class="suggestion-actions">
              <button class="action-btn" @click="viewSuggestionDetail(suggestion)">
                查看详情
              </button>
              <button class="action-btn primary" @click="startApplication(suggestion)">
                立即申请
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 政策日历 -->
      <div class="policy-calendar">
        <div class="section-header">
          <h4>政策日历</h4>
          <div class="calendar-nav">
            <i class="fas fa-chevron-left" @click="previousMonth"></i>
            <span class="current-month">{{ currentMonth }}</span>
            <i class="fas fa-chevron-right" @click="nextMonth"></i>
          </div>
        </div>
        <div class="calendar-events">
          <div 
            class="event-item" 
            v-for="event in calendarEvents" 
            :key="event.id"
            @click="viewEventDetail(event)"
          >
            <div class="event-date">
              <div class="event-day">{{ event.day }}</div>
              <div class="event-month">{{ event.month }}</div>
            </div>
            <div class="event-info">
              <h5>{{ event.title }}</h5>
              <p>{{ event.description }}</p>
              <div class="event-type" :class="event.type">
                {{ getEventTypeText(event.type) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 完善画像 -->
      <div class="profile-improvement">
        <div class="improvement-card">
          <div class="improvement-header">
            <div class="improvement-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="improvement-info">
              <h4>完善企业画像</h4>
              <p>提供更多信息，获得更精准的政策推荐</p>
            </div>
          </div>
          <div class="improvement-items">
            <div 
              class="improvement-item" 
              v-for="item in improvementItems" 
              :key="item.id"
              @click="improveProfile(item)"
            >
              <div class="item-info">
                <h5>{{ item.title }}</h5>
                <p>{{ item.description }}</p>
              </div>
              <div class="item-benefit">+{{ item.scoreIncrease }}分</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script>
import BottomNavigation from '@/components/BottomNavigation.vue'

export default {
  name: 'CompanyProfile',
  components: {
    BottomNavigation
  },
  data() {
    return {
      profileScore: 78,
      analysisTime: '2024-01-25 09:30 更新',
      currentMonth: '2024年2月',
      companyInfo: {
        name: '某智能制造科技有限公司',
        industry: '智能制造',
        scale: '中型企业',
        tags: ['高新技术企业', '专精特新', '制造业单项冠军'],
        establishedYear: 2018,
        employees: 156,
        revenue: 8500,
        rdRatio: 8.5
      },
      matchingStats: {
        totalPolicies: 23,
        highMatch: 8,
        potentialValue: 1200
      },
      policyCategories: [
        {
          id: 1,
          name: '数字化转型',
          description: '智能制造、工业互联网等数字化改造政策',
          icon: 'fas fa-digital-tachograph',
          color: '#1890ff',
          matchScore: 95,
          policyCount: 8,
          topPolicies: [
            { id: 1, title: '制造业数字化转型专项资金', maxValue: '最高500万' },
            { id: 2, title: '工业互联网创新发展工程', maxValue: '最高300万' }
          ]
        },
        {
          id: 2,
          name: '技术创新',
          description: '研发投入、技术改造、创新平台建设',
          icon: 'fas fa-lightbulb',
          color: '#52c41a',
          matchScore: 88,
          policyCount: 6,
          topPolicies: [
            { id: 3, title: '企业技术中心认定', maxValue: '最高200万' },
            { id: 4, title: '科技创新券支持', maxValue: '最高50万' }
          ]
        },
        {
          id: 3,
          name: '税收优惠',
          description: '研发费用加计扣除、高新技术企业等税收政策',
          icon: 'fas fa-percentage',
          color: '#fa8c16',
          matchScore: 92,
          policyCount: 5,
          topPolicies: [
            { id: 5, title: '研发费用加计扣除', maxValue: '175%扣除' },
            { id: 6, title: '高新技术企业所得税优惠', maxValue: '15%税率' }
          ]
        },
        {
          id: 4,
          name: '人才支持',
          description: '人才引进、培训补贴、技能提升等人才政策',
          icon: 'fas fa-user-graduate',
          color: '#722ed1',
          matchScore: 75,
          policyCount: 4,
          topPolicies: [
            { id: 7, title: '高层次人才引进补贴', maxValue: '最高100万' },
            { id: 8, title: '职工技能培训补贴', maxValue: '最高20万' }
          ]
        }
      ],
      characteristics: [
        {
          id: 1,
          name: '创新能力',
          icon: 'fas fa-rocket',
          score: 85,
          color: '#1890ff',
          description: '研发投入占比高，拥有多项专利和技术成果'
        },
        {
          id: 2,
          name: '数字化水平',
          icon: 'fas fa-laptop-code',
          score: 78,
          color: '#52c41a',
          description: '已实施部分数字化改造，具备进一步升级基础'
        },
        {
          id: 3,
          name: '财务状况',
          icon: 'fas fa-chart-pie',
          score: 82,
          color: '#fa8c16',
          description: '财务状况良好，具备政策申请的资金配套能力'
        },
        {
          id: 4,
          name: '合规程度',
          icon: 'fas fa-shield-alt',
          score: 90,
          color: '#722ed1',
          description: '企业合规经营，无重大违法违规记录'
        }
      ],
      applicationSuggestions: [
        {
          id: 1,
          title: '制造业数字化转型专项资金',
          description: '基于您的数字化水平和创新能力，建议优先申请此项政策',
          icon: 'fas fa-star',
          priority: 'high'
        },
        {
          id: 2,
          title: '企业技术中心认定',
          description: '您的研发投入和创新成果符合认定条件',
          icon: 'fas fa-award',
          priority: 'medium'
        },
        {
          id: 3,
          title: '专精特新小巨人企业认定',
          description: '建议完善相关材料后申请认定',
          icon: 'fas fa-gem',
          priority: 'low'
        }
      ],
      calendarEvents: [
        {
          id: 1,
          day: '15',
          month: '2月',
          title: '数字化转型专项资金申报截止',
          description: '申报截止日期临近，请尽快提交材料',
          type: 'deadline'
        },
        {
          id: 2,
          day: '20',
          month: '2月',
          title: '高新技术企业认定政策解读会',
          description: '专家解读最新认定政策和申报要点',
          type: 'event'
        },
        {
          id: 3,
          day: '28',
          month: '2月',
          title: '企业技术中心申报开始',
          description: '新一轮企业技术中心认定申报启动',
          type: 'opportunity'
        }
      ],
      improvementItems: [
        {
          id: 1,
          title: '上传财务报表',
          description: '提供近三年审计报告，提高财务评估准确性',
          scoreIncrease: 8
        },
        {
          id: 2,
          title: '完善知识产权信息',
          description: '补充专利、商标等知识产权详细信息',
          scoreIncrease: 6
        },
        {
          id: 3,
          title: '更新组织架构',
          description: '提供最新的组织架构和人员配置信息',
          scoreIncrease: 4
        }
      ]
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    refreshProfile() {
      // 刷新企业画像
      alert('正在更新企业画像...')
    },
    viewCategoryDetail(category) {
      this.$router.push(`/policy-category/${category.id}`)
    },
    viewPolicyDetail(policy) {
      this.$router.push(`/policy/${policy.id}`)
    },
    viewSuggestionDetail(suggestion) {
      this.$router.push(`/suggestion/${suggestion.id}`)
    },
    startApplication(suggestion) {
      this.$router.push(`/apply/${suggestion.id}`)
    },
    previousMonth() {
      // 切换到上个月
      alert('切换到上个月')
    },
    nextMonth() {
      // 切换到下个月
      alert('切换到下个月')
    },
    viewEventDetail(event) {
      this.$router.push(`/event/${event.id}`)
    },
    improveProfile(item) {
      this.$router.push(`/improve-profile/${item.id}`)
    },
    getPriorityText(priority) {
      const priorityMap = {
        'high': '高优先级',
        'medium': '中优先级',
        'low': '低优先级'
      }
      return priorityMap[priority] || '未知'
    },
    getEventTypeText(type) {
      const typeMap = {
        'deadline': '截止提醒',
        'event': '活动通知',
        'opportunity': '申报机会'
      }
      return typeMap[type] || '其他'
    }
  }
}
</script>

<style scoped>
/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-actions i {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

/* 企业基本信息 */
.company-basic-info {
  padding: 16px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.info-card {
  color: white;
}

.company-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.company-logo {
  width: 50px;
  height: 50px;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.company-logo i {
  font-size: 24px;
}

.company-details {
  flex: 1;
}

.company-details h3 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 4px;
}

.company-details p {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.company-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.company-tags .tag {
  background: rgba(255,255,255,0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.profile-score {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
}

.score {
  font-size: 18px;
  font-weight: 600;
}

.score-label {
  font-size: 10px;
  opacity: 0.8;
}

.company-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 16px;
  font-weight: 600;
}

.stat-label {
  font-size: 11px;
  opacity: 0.8;
  margin-top: 2px;
}

/* 政策匹配分析 */
.policy-matching-analysis {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.analysis-time {
  font-size: 12px;
  color: #999;
}

.matching-overview {
  margin-bottom: 20px;
}

.overview-stats {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-number {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
}

.overview-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.matching-categories {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-item {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.category-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.category-icon i {
  font-size: 18px;
  color: white;
}

.category-info {
  flex: 1;
}

.category-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.category-info p {
  font-size: 12px;
  color: #666;
}

.category-match {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.match-score {
  font-size: 18px;
  font-weight: 600;
  color: #52c41a;
}

.policy-count {
  font-size: 11px;
  color: #999;
}

.category-policies {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.policy-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.policy-preview:hover {
  background: #f0f8ff;
}

.policy-title {
  font-size: 13px;
  color: #333;
  flex: 1;
}

.policy-value {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

/* 企业特征分析 */
.company-characteristics {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.company-characteristics h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.characteristics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.characteristic-item {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

.char-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.char-icon {
  width: 32px;
  height: 32px;
  background: #f0f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.char-icon i {
  font-size: 14px;
  color: #1890ff;
}

.char-header h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.char-score {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.score-bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  transition: width 0.3s;
}

.score-text {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.char-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 申请建议 */
.application-suggestions {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.application-suggestions h4 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #d9d9d9;
}

.suggestion-item.high {
  border-left-color: #ff4d4f;
  background: #fff2f0;
}

.suggestion-item.medium {
  border-left-color: #fa8c16;
  background: #fff7e6;
}

.suggestion-item.low {
  border-left-color: #52c41a;
  background: #f6ffed;
}

.suggestion-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.suggestion-icon {
  width: 32px;
  height: 32px;
  background: #f0f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.suggestion-icon i {
  font-size: 14px;
  color: #1890ff;
}

.suggestion-info {
  flex: 1;
}

.suggestion-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.suggestion-info p {
  font-size: 12px;
  color: #666;
}

.suggestion-priority {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  background: #f0f0f0;
  color: #666;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.action-btn.primary {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.action-btn.primary:hover {
  background: #40a9ff;
}

/* 政策日历 */
.policy-calendar {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: 12px;
}

.calendar-nav i {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.current-month {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.calendar-events {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.event-item:hover {
  background: #f0f0f0;
}

.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40px;
  flex-shrink: 0;
}

.event-day {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.event-month {
  font-size: 11px;
  color: #999;
}

.event-info {
  flex: 1;
}

.event-info h5 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.event-info p {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.event-type {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
}

.event-type.deadline {
  background: #fff2f0;
  color: #ff4d4f;
}

.event-type.event {
  background: #e6f7ff;
  color: #1890ff;
}

.event-type.opportunity {
  background: #f6ffed;
  color: #52c41a;
}

/* 完善画像 */
.profile-improvement {
  padding: 16px;
  background: white;
}

.improvement-card {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

.improvement-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.improvement-icon {
  width: 40px;
  height: 40px;
  background: #f0f8ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.improvement-icon i {
  font-size: 18px;
  color: #1890ff;
}

.improvement-info h4 {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.improvement-info p {
  font-size: 13px;
  color: #666;
}

.improvement-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.improvement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.improvement-item:hover {
  background: #f0f8ff;
}

.item-info h5 {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.item-info p {
  font-size: 11px;
  color: #666;
}

.item-benefit {
  font-size: 12px;
  font-weight: 500;
  color: #52c41a;
}
</style>
